const { createServer } = require('http'); // Need http server for WS
const WebSocket = require('ws');
const fs = require('fs');
const fsPromises = require('fs/promises');
const path = require('path');

const wsPort = process.env.WS_PORT || 6656; // WebSocket Port

// --- File System Constants (Copied from server.js) ---
const PUBLIC_UPLOADS_DIR = path.join(process.cwd(), 'public/uploads');
const PORTAL_UPLOADS_DIR = path.join(process.cwd(), 'uploads', 'portals');
const PORTALS_DATA_DIR = path.join(process.cwd(), 'data', 'upload-portals');
const PORTALS_FILE = path.join(PORTALS_DATA_DIR, 'portals.json');

// --- File Status Management ---
const FILE_STATUS_DIR = path.join(process.cwd(), 'data', 'file-status');
const FILE_STATUS_FILE = path.join(FILE_STATUS_DIR, 'sync-status.json');

// 文件状态枚举
const FILE_STATUS = {
  PENDING: '未同步',
  UPLOADING: '同步中', 
  UPLOADED: '已同步',
  FAILED: '同步失败'
};

// 内存中的文件状态缓存
let fileStatusCache = {};

// 连接的客户端列表
const connectedClients = new Set();

// --- Helper Functions (Copied from server.js) ---
const ensureDirectoryExists = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

const getPortals = () => {
  try {
    ensureDirectoryExists(PORTALS_DATA_DIR);
    if (!fs.existsSync(PORTALS_FILE)) return [];
    const data = fs.readFileSync(PORTALS_FILE, 'utf8');
    return JSON.parse(data || '[]'); // Handle empty file
  } catch (err) {
    console.error('[WS Server] Error reading portals file:', err);
    return [];
  }
};

const parseAdminPath = (reqPath) => {
  const normalizedReqPath = (reqPath || '/').replace(/^\/+/, '');
  const pathSegments = normalizedReqPath.split('/').filter(Boolean);
  const portals = getPortals();

  if (pathSegments.length === 0) {
    return { type: 'root', logicalPath: '/', physicalPath: '/', portals };
  }

  const firstSegment = pathSegments[0];
  const potentialPortal = portals.find(p => p.name === firstSegment);

  if (potentialPortal) {
    const portalId = potentialPortal.id;
    const portalName = potentialPortal.name;
    const logicalPath = pathSegments.join('/').replace(/\\/g, '/');

    if (pathSegments.length === 1) {
      const physicalPath = path.join(PORTAL_UPLOADS_DIR, portalId);
      return { type: 'portal_root', portalId, portalName, physicalPath, logicalPath };
    } else {
      const deviceId = pathSegments[1];
      const relativePathInsideDevice = pathSegments.slice(2).join('/');
      const physicalPath = path.join(PORTAL_UPLOADS_DIR, portalId, deviceId, relativePathInsideDevice);
      const relativePath = path.join(deviceId, relativePathInsideDevice).replace(/\\/g, '/');
      return {
        type: 'portal_content',
        portalId,
        portalName,
        deviceId,
        physicalPath,
        logicalPath,
        relativePath
      };
    }
  } else {
    const physicalPath = path.join(PUBLIC_UPLOADS_DIR, normalizedReqPath);
    const logicalPath = normalizedReqPath;
    return { type: 'public', physicalPath, logicalPath };
  }
};

// --- Recursive File Tree Function (Copied from server.js) ---
async function getAdminFileTreeRecursive(logicalPathReq) {
  const pathContext = parseAdminPath(logicalPathReq);
  const tree = { files: [], folders: [] };
  let currentPhysicalPath = '';
  let isRoot = false;

  console.log(`[WS Server - getTree] Requested Logical Path: ${logicalPathReq}, Context:`, pathContext);

  try {
    if (pathContext.type === 'root') {
      isRoot = true;
      const portals = pathContext.portals || [];
      const portalNames = new Set();
      for (const portal of portals) {
        portalNames.add(portal.name);
        const portalFolderNode = {
          name: portal.name,
          filename: portal.name,
          isFolder: true,
          isPortal: true,
          path: portal.name,
          createdAt: portal.createdAt,
          modifiedAt: portal.updatedAt,
          size: portal.stats?.totalSize || 0,
          fileCount: portal.stats?.uploads || 0,
          syncStatus: '未知',
          children: await getAdminFileTreeRecursive(portal.name)
        };
        tree.folders.push(portalFolderNode);
      }

      ensureDirectoryExists(PUBLIC_UPLOADS_DIR);
      const publicItems = await fsPromises.readdir(PUBLIC_UPLOADS_DIR, { withFileTypes: true });
      for (const item of publicItems) {
        const itemLogicalPath = item.name;
        if (portalNames.has(item.name)) {
            console.log(`[WS Server - getTree] Skipping public item '${item.name}' as it conflicts with a portal name.`);
            continue;
        }
        const itemPhysicalPath = path.join(PUBLIC_UPLOADS_DIR, item.name);
        try {
          const stats = await fsPromises.stat(itemPhysicalPath);
          if (item.isDirectory()) {
            tree.folders.push({
              name: item.name,
              filename: item.name,
              isFolder: true,
              isPortal: false,
              path: itemLogicalPath,
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime,
              size: 0,
              syncStatus: '未同步',
              children: await getAdminFileTreeRecursive(itemLogicalPath)
            });
          } else if (item.isFile()) {
            const filePath = itemLogicalPath;
            const fileStatus = getFileStatus(filePath);
            
            tree.files.push({
              name: item.name,
              filename: item.name,
              isFolder: false,
              isPortalFile: false,
              path: itemLogicalPath,
              size: stats.size,
              type: path.extname(item.name).slice(1).toLowerCase() || 'unknown',
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime,
              syncStatus: fileStatus
            });
          }
        } catch (statErr) {
          console.error(`[WS Server - getTree] Error stating public item ${itemPhysicalPath}:`, statErr);
        }
      }
    } else {
      currentPhysicalPath = pathContext.physicalPath;
      ensureDirectoryExists(currentPhysicalPath);
      const items = await fsPromises.readdir(currentPhysicalPath, { withFileTypes: true });

      for (const item of items) {
        const itemLogicalPath = path.join(pathContext.logicalPath, item.name).replace(/\\/g, '/');
        const itemPhysicalPath = path.join(currentPhysicalPath, item.name);
        const isDeviceFolder = pathContext.type === 'portal_root';
        const isPortalFile = pathContext.type === 'portal_content';

        try {
          const stats = await fsPromises.stat(itemPhysicalPath);
          if (item.isDirectory()) {
            tree.folders.push({
              name: item.name,
              filename: item.name,
              isFolder: true,
              isPortal: false,
              isDeviceFolder: isDeviceFolder,
              portalId: pathContext.portalId || null,
              deviceId: isDeviceFolder ? item.name : (pathContext.deviceId || null),
              path: itemLogicalPath,
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime,
              size: 0,
              syncStatus: '未同步',
              children: await getAdminFileTreeRecursive(itemLogicalPath)
            });
          } else if (item.isFile()) {
            // === 修复：为portal文件构建完整的逻辑路径 ===
            let fullLogicalPath = itemLogicalPath;
            
            if (isPortalFile && pathContext.portalName && pathContext.deviceId) {
              // 构建完整的逻辑路径：portal名称/deviceId/相对路径
              const relativePath = pathContext.relativePath || '';
              const fileName = item.name;
              
              // 如果有子路径（文件在文件夹中），需要包含文件夹路径
              if (relativePath && relativePath !== fileName) {
                // 提取文件夹路径（除去deviceId部分）
                const folderPath = relativePath.replace(new RegExp(`^${pathContext.deviceId}/?`), '');
                if (folderPath) {
                  fullLogicalPath = `${pathContext.portalName}/${pathContext.deviceId}/${folderPath}/${fileName}`;
                } else {
                  fullLogicalPath = `${pathContext.portalName}/${pathContext.deviceId}/${fileName}`;
                }
              } else {
                fullLogicalPath = `${pathContext.portalName}/${pathContext.deviceId}/${fileName}`;
              }
              
              console.log(`[WS Server - getTree] Portal文件完整路径构建: ${itemLogicalPath} -> ${fullLogicalPath}`, {
                portalName: pathContext.portalName,
                deviceId: pathContext.deviceId,
                relativePath: relativePath,
                fileName: fileName
              });
            }
            
            const fileStatus = getFileStatus(fullLogicalPath);
            
            tree.files.push({
              name: item.name,
              filename: item.name,
              isFolder: false,
              isPortalFile: isPortalFile,
              path: fullLogicalPath, // 使用完整逻辑路径
              originalPath: itemLogicalPath, // 保留原始路径用于其他用途
              size: stats.size,
              type: path.extname(item.name).slice(1).toLowerCase() || 'unknown',
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime,
              syncStatus: fileStatus,
              // === 新增：portal相关信息 ===
              portalName: pathContext.portalName || null,
              deviceId: pathContext.deviceId || null,
              portalId: pathContext.portalId || null
            });
          }
        } catch (statErr) {
          console.error(`[WS Server - getTree] Error stating item ${itemPhysicalPath}:`, statErr);
        }
      }
    }
  } catch (err) {
    if (err.code === 'ENOENT') {
      console.warn(`[WS Server - getTree] Directory not found: ${currentPhysicalPath} (Logical: ${logicalPathReq})`);
    } else {
      console.error(`[WS Server - getTree] Error reading directory ${currentPhysicalPath} (Logical: ${logicalPathReq}):`, err);
    }
  }
  return tree;
}

// --- WebSocket Server Setup ---
// Create a simple HTTP server solely for the WebSocket upgrade
const dummyHttpServer = createServer((req, res) => {
  res.writeHead(404, { 'Content-Type': 'text/plain' });
  res.end('WebSocket Only Server');
});

const wss = new WebSocket.Server({ server: dummyHttpServer });

wss.on('connection', (ws) => {
    console.log('[WS Server] Client Connected');
    
    // 将客户端添加到连接列表
    connectedClients.add(ws);

    ws.on('message', async (messageBuffer) => {
      const messageString = messageBuffer.toString();
      console.log('[WS Server] Received message:', messageString);
      let request;
      try {
        request = JSON.parse(messageString);
      } catch (e) {
        console.error('[WS Server] Invalid JSON received:', e);
        ws.send(JSON.stringify({ type: 'error', message: 'Invalid JSON format' }));
        return;
      }

      // Handle get_full_admin_file_tree action by checking 'type'
      if (request.type === 'get_full_admin_file_tree') {
        console.log('[WS Server] Processing get_full_admin_file_tree request...');
        try {
          const adminTree = await getAdminFileTreeRecursive('/'); // Start recursion from root
          console.log('[WS Server] Sending file tree to client.');
          ws.send(JSON.stringify({ type: 'admin_file_tree', payload: adminTree }));
        } catch (error) {
          console.error('[WS Server] Error generating file tree:', error);
          ws.send(JSON.stringify({ type: 'error', message: 'Failed to generate file tree' }));
        }
      } 
      // Handle file status update
      else if (request.type === 'update_file_status') {
        console.log('[WS Server] Processing file status update:', request.filename, '->', request.status);
        console.log('[WS Server] 完整请求元数据:', {
          filename: request.filename,
          status: request.status,
          forceUpdate: request.forceUpdate,
          isNewUpload: request.isNewUpload,
          reason: request.reason,
          source: request.source,
          processId: request.processId,
          timestamp: request.timestamp
        });
        
        try {
          if (!request.filename || !request.status) {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Missing filename or status' 
            }));
            return;
          }
          
          const success = await updateFileStatus(request.filename, request.status, {
            updatedBy: 'client',
            clientTimestamp: request.timestamp,
            forceUpdate: request.forceUpdate || false,
            isNewUpload: request.isNewUpload || false,
            reason: request.reason || 'status_update',
            source: request.source || 'websocket_client',
            processId: request.processId || 'unknown'
          });
          
          if (success) {
            ws.send(JSON.stringify({ 
              type: 'file_status_update_response', 
              filename: request.filename,
              status: request.status,
              success: true 
            }));
            
            // 如果是上传完成状态，触发文件树刷新
            if (request.status === FILE_STATUS.UPLOADED) {
              console.log('[WS Server] File uploaded, broadcasting tree update');
              setTimeout(async () => {
                try {
                  const adminTree = await getAdminFileTreeRecursive('/');
                  broadcastToClients({ 
                    type: 'admin_file_tree', 
                    payload: adminTree,
                    reason: 'file_status_change'
                  });
                } catch (error) {
                  console.error('[WS Server] Error refreshing tree after status update:', error);
                }
              }, 500); // 延迟500ms确保状态已保存
            }
          } else {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Failed to update file status' 
            }));
          }
        } catch (error) {
          console.error('[WS Server] Error updating file status:', error);
          ws.send(JSON.stringify({ 
            type: 'error', 
            message: 'Internal error updating file status' 
          }));
        }
      }
      // Handle file status removal
      else if (request.type === 'remove_file_status') {
        console.log('[WS Server] Processing file status removal:', request.filename, 'isFolder:', request.isFolder);
        try {
          if (!request.filename) {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Missing filename for removal' 
            }));
            return;
          }
          
          const success = await removeFileStatus(request.filename, request.isFolder);
          
          if (success) {
            ws.send(JSON.stringify({ 
              type: 'file_status_remove_response', 
              filename: request.filename,
              success: true 
            }));
            
            // 广播状态移除给所有连接的客户端
            broadcastToClients({
              type: 'file_status_removed',
              filePath: request.filename,
              isFolder: request.isFolder,
              timestamp: Date.now()
            });
          } else {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Failed to remove file status' 
            }));
          }
        } catch (error) {
          console.error('[WS Server] Error removing file status:', error);
          ws.send(JSON.stringify({ 
            type: 'error', 
            message: 'Internal error removing file status' 
          }));
        }
      }
      // Handle file status query
      else if (request.type === 'get_file_status') {
        try {
          if (!request.filename) {
            ws.send(JSON.stringify({ 
              type: 'error', 
              message: 'Missing filename' 
            }));
            return;
          }
          
          const status = getFileStatus(request.filename);
          ws.send(JSON.stringify({ 
            type: 'file_status_response', 
            filename: request.filename,
            status: status
          }));
        } catch (error) {
          console.error('[WS Server] Error querying file status:', error);
          ws.send(JSON.stringify({ 
            type: 'error', 
            message: 'Internal error querying file status' 
          }));
        }
      }
      // Handle ping request
      else if (request.type === 'ping') {
        console.log('[WS Server] Received ping, sending pong.');
        ws.send(JSON.stringify({ type: 'pong' }));
      }
      // Handle request for all status data (for diagnosis tool)
      else if (request.type === 'request_all_statuses') {
        console.log('[WS Server] Received request for all status data');
        try {
          ws.send(JSON.stringify({ 
            type: 'all_statuses_response',
            requestId: request.requestId,
            statuses: fileStatusCache,
            timestamp: Date.now()
          }));
        } catch (error) {
          console.error('[WS Server] Error sending all statuses:', error);
          ws.send(JSON.stringify({ 
            type: 'error', 
            message: 'Failed to get all statuses' 
          }));
        }
      }
      // Handle request for extension status data (for diagnosis tool)
      else if (request.type === 'request_extension_status') {
        console.log('[WS Server] Received request for extension status data');
        try {
          // 此消息将被转发到浏览器插件（如果连接的话）
          // 目前返回空数据，实际数据需要插件通过其他方式提供
          ws.send(JSON.stringify({ 
            type: 'extension_status_response',
            requestId: request.requestId,
            status: {
              uploadedFiles: [],
              uploadStates: {},
              cachedFiles: [],
              note: 'Extension data not available via WebSocket, use direct extension access'
            },
            timestamp: Date.now()
          }));
        } catch (error) {
          console.error('[WS Server] Error handling extension status request:', error);
          ws.send(JSON.stringify({ 
            type: 'error', 
            message: 'Failed to get extension status' 
          }));
        }
      }
      // Handle init message (just acknowledge)
      else if (request.type === 'init') {
        console.log('[WS Server] Received init message from client:', request.client, request.version);
        // 发送当前文件状态摘要
        ws.send(JSON.stringify({ 
          type: 'init_response', 
          message: 'Connected to server',
          fileStatusCount: Object.keys(fileStatusCache).length
        }));
      }
      else {
        // Log the type if the action is unknown
        console.log('[WS Server] Unknown message type received:', request.type); 
      }
    });

    ws.on('close', () => {
      console.log('[WS Server] Client Disconnected');
      connectedClients.delete(ws);
    });

    ws.on('error', (error) => {
      console.error('[WS Server] Error:', error);
      connectedClients.delete(ws);
    });
});

dummyHttpServer.listen(wsPort, '0.0.0.0', async () => {
  console.log(`> WebSocket Server ready on ws://0.0.0.0:${wsPort}`);
  
  // 初始化文件状态管理
  console.log('[WS Server] Initializing file status management...');
  await loadFileStatus();
  
  // 启动时执行一次状态一致性检查
  console.log('[WS Server] 启动状态一致性检查...');
  await performStatusConsistencyCheck();
  
  // 设置定期保存文件状态（防止数据丢失）
  setInterval(async () => {
    await saveFileStatus();
  }, 30000); // 每30秒保存一次
  
  // 设置定期状态一致性检查（清理无效状态）
  setInterval(async () => {
    console.log('[WS Server] 执行定期状态一致性检查...');
    await performStatusConsistencyCheck();
  }, 300000); // 每5分钟检查一次
  
  console.log('[WS Server] File status management initialized');
});

console.log(`Attempting to start WebSocket server on port ${wsPort}...`);

// 优雅关闭处理
process.on('SIGTERM', async () => {
  console.log('[WS Server] Received SIGTERM, saving file status...');
  await saveFileStatus();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('[WS Server] Received SIGINT, saving file status...');
  await saveFileStatus();
  process.exit(0);
});

// --- File Status Management Functions ---

// 加载文件状态
async function loadFileStatus() {
  try {
    ensureDirectoryExists(FILE_STATUS_DIR);
    if (fs.existsSync(FILE_STATUS_FILE)) {
      const data = await fsPromises.readFile(FILE_STATUS_FILE, 'utf8');
      fileStatusCache = JSON.parse(data) || {};
      console.log(`[WS Server] 已加载 ${Object.keys(fileStatusCache).length} 个文件状态`);
    } else {
      fileStatusCache = {};
      console.log('[WS Server] 文件状态文件不存在，初始化空缓存');
    }
  } catch (error) {
    console.error('[WS Server] 加载文件状态失败:', error);
    fileStatusCache = {};
  }
}

// 保存文件状态
async function saveFileStatus() {
  try {
    ensureDirectoryExists(FILE_STATUS_DIR);
    await fsPromises.writeFile(FILE_STATUS_FILE, JSON.stringify(fileStatusCache, null, 2));
    console.log(`[WS Server] 已保存 ${Object.keys(fileStatusCache).length} 个文件状态`);
  } catch (error) {
    console.error('[WS Server] 保存文件状态失败:', error);
  }
}

// 更新文件状态
async function updateFileStatus(filePath, status, metadata = {}) {
  const normalizedPath = filePath.replace(/\\/g, '/');
  let previousStatus = fileStatusCache[normalizedPath]?.status;
  let actualFilePath = normalizedPath; // 实际使用的文件路径
  
  // === 新增：智能路径匹配逻辑 ===
  console.log(`[WS Server] 开始状态更新: ${normalizedPath} -> ${status}`);
  
  // 检查精确匹配
  if (!fileStatusCache[normalizedPath]) {
    console.log(`[WS Server] 精确路径匹配失败，尝试模糊匹配: ${normalizedPath}`);
    
    // 尝试模糊匹配：基于文件名查找
    const targetFileName = normalizedPath.split('/').pop(); // 提取文件名
    const matchingPaths = [];
    
    // 遍历现有状态缓存，寻找匹配的文件名
    Object.keys(fileStatusCache).forEach(existingPath => {
      const existingFileName = existingPath.split('/').pop();
      if (existingFileName === targetFileName) {
        matchingPaths.push(existingPath);
        console.log(`[WS Server] 发现文件名匹配: ${existingPath}`);
      }
    });
    
    if (matchingPaths.length === 1) {
      // 找到唯一匹配，使用该路径
      actualFilePath = matchingPaths[0];
      previousStatus = fileStatusCache[actualFilePath]?.status;
      console.log(`[WS Server] 使用模糊匹配路径: ${normalizedPath} -> ${actualFilePath}`);
    } else if (matchingPaths.length > 1) {
      // 多个匹配，尝试智能选择
      console.log(`[WS Server] 发现多个匹配路径: ${matchingPaths.join(', ')}`);
      
      // 优先选择包含输入路径部分的匹配
      const bestMatch = matchingPaths.find(path => {
        const inputParts = normalizedPath.split('/').filter(Boolean);
        const pathParts = path.split('/').filter(Boolean);
        
        // 检查是否包含输入路径的关键部分
        return inputParts.every(inputPart => 
          pathParts.some(pathPart => pathPart.includes(inputPart) || inputPart.includes(pathPart))
        );
      });
      
      if (bestMatch) {
        actualFilePath = bestMatch;
        previousStatus = fileStatusCache[actualFilePath]?.status;
        console.log(`[WS Server] 使用最佳匹配路径: ${normalizedPath} -> ${actualFilePath}`);
      } else {
        // 如果没有最佳匹配，使用第一个匹配
        actualFilePath = matchingPaths[0];
        previousStatus = fileStatusCache[actualFilePath]?.status;
        console.log(`[WS Server] 使用第一个匹配路径: ${normalizedPath} -> ${actualFilePath}`);
      }
    } else {
      console.log(`[WS Server] 未找到匹配的文件名，继续使用原路径: ${normalizedPath}`);
    }
  } else {
    console.log(`[WS Server] 精确路径匹配成功: ${normalizedPath}`);
  }
  
  // === 强化的文件存在性验证（对所有状态更新） ===
  console.log(`[WS Server] 开始验证文件存在性: ${actualFilePath}`);
  const fileExists = await verifyFileExists(actualFilePath);
  
  if (!fileExists) {
    console.warn(`[WS Server] 文件不存在，拒绝状态更新: ${actualFilePath} -> ${status}`);
    
    // 如果文件不存在，无论要设置什么状态，都先清理现有状态记录
    if (fileStatusCache[actualFilePath]) {
      console.log(`[WS Server] 清理不存在文件的状态记录: ${actualFilePath} (原状态: ${previousStatus})`);
      await removeFileStatus(actualFilePath, false);
    }
    
    // 广播状态移除消息给所有客户端
    broadcastToClients({
      type: 'file_status_removed',
      filePath: actualFilePath,
      isFolder: false,
      timestamp: Date.now(),
      reason: 'file_not_found_on_status_update',
      attemptedStatus: status,
      originalRequestPath: normalizedPath
    });
    
    return false; // 返回false表示更新失败
  }
  
  // === 特殊处理：新上传文件的状态重置 ===
  if (metadata.isNewUpload && (status === FILE_STATUS.PENDING || status === '未同步')) {
    console.log(`[WS Server] 新上传文件状态重置: ${actualFilePath} -> ${status}`);
    
    // 对于新上传文件，先强制清理可能存在的旧状态
    if (fileStatusCache[actualFilePath] && fileStatusCache[actualFilePath].status !== status) {
      console.log(`[WS Server] 清理新上传文件的旧状态: ${actualFilePath} (${fileStatusCache[actualFilePath].status} -> ${status})`);
      
      // 广播旧状态的移除
      broadcastToClients({
        type: 'file_status_removed',
        filePath: actualFilePath,
        isFolder: false,
        timestamp: Date.now(),
        reason: 'new_upload_status_reset'
      });
    }
  }
  
  // === 防止状态回退检查 ===
  // 重要：文件存在性 ≠ 同步状态。文件可能存在于服务器但尚未通过插件上传到云盘
  if (metadata.forceUpdate !== true) {
    // 防止从"已同步"状态意外回退到其他状态（除非是强制更新）
    if (previousStatus === FILE_STATUS.UPLOADED || previousStatus === '已同步') {
      if (status !== FILE_STATUS.UPLOADED && status !== '已同步') {
        console.warn(`[WS Server] 防止状态回退: ${actualFilePath} 从 ${previousStatus} 回退到 ${status}，已忽略`);
        
        // 除非是新上传文件或明确的强制更新，否则拒绝状态回退
        if (!metadata.isNewUpload) {
          console.log(`[WS Server] 保持现有状态: ${actualFilePath} = ${previousStatus}`);
          return true; // 返回true但不更新状态
        }
      }
    }
  } else {
    // === 强制更新模式：允许任何状态变更，包括从"已同步"回退到"未同步" ===
    console.log(`[WS Server] 强制更新模式: ${actualFilePath} 从 ${previousStatus || '(无)'} 强制更新到 ${status}`, {
      reason: metadata.reason || 'force_update',
      source: metadata.source || 'unknown'
    });
  }
  
  // === 执行状态更新 ===
  fileStatusCache[actualFilePath] = {
    status,
    updatedAt: new Date().toISOString(),
    metadata,
    ...metadata
  };
  
  console.log(`[WS Server] 文件状态更新: ${actualFilePath} ${previousStatus || '(无)'} -> ${status}`, {
    fileExists: true,
    isNewUpload: metadata.isNewUpload || false,
    forceUpdate: metadata.forceUpdate || false,
    reason: metadata.reason || 'status_update',
    originalRequestPath: normalizedPath !== actualFilePath ? normalizedPath : null
  });
  
  // 立即保存状态
  await saveFileStatus();
  
  // 同步到HTTP API的状态文件
  await syncToHttpApiStatus(actualFilePath, status);
  
  // 广播状态更新给所有连接的客户端
  broadcastToClients({
    type: 'file_status_updated',
    filePath: actualFilePath,
    status,
    previousStatus,
    timestamp: Date.now(),
    isNewUpload: metadata.isNewUpload || false,
    originalRequestPath: normalizedPath !== actualFilePath ? normalizedPath : null
  });
  
  return true;
}

// 验证文件是否存在的辅助函数
async function verifyFileExists(filePath) {
  try {
    // === 增强：特殊处理portal文件路径格式 ===
    console.log(`[WS Server] 开始验证文件存在性: ${filePath}`);
    
    // 检查是否是portal文件路径格式（portal名称/deviceId/文件路径）
    const pathParts = filePath.split('/').filter(Boolean);
    let physicalPath = '';
    let isPortalFile = false;
    
    if (pathParts.length >= 3) {
      // 可能是portal文件路径：portal名称/deviceId/文件路径
      const potentialPortalName = pathParts[0];
      const potentialDeviceId = pathParts[1];
      const actualFilePath = pathParts.slice(2).join('/');
      
      // 检查是否匹配UUID格式的设备ID
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      
      if (uuidRegex.test(potentialDeviceId)) {
        console.log(`[WS Server] 检测到portal文件路径格式: portal=${potentialPortalName}, device=${potentialDeviceId}, file=${actualFilePath}`);
        
        // 尝试从portals配置中找到对应的portal
        const portals = getPortals();
        const matchingPortal = portals.find(p => p.name === potentialPortalName);
        
        if (matchingPortal) {
          // 构建物理路径
          physicalPath = path.join(PORTAL_UPLOADS_DIR, matchingPortal.id, potentialDeviceId, actualFilePath);
          isPortalFile = true;
          console.log(`[WS Server] Portal文件物理路径: ${physicalPath}`);
        } else {
          console.log(`[WS Server] 未找到匹配的portal: ${potentialPortalName}`);
        }
      }
    }
    
    // 如果不是portal文件格式，使用原有逻辑
    if (!isPortalFile) {
      // 解析文件路径，判断是在public uploads还是portal uploads中
      const pathContext = parseAdminPath(filePath);
      
      if (pathContext.type === 'public') {
        physicalPath = pathContext.physicalPath;
      } else if (pathContext.type === 'portal_content') {
        physicalPath = pathContext.physicalPath;
      } else if (pathContext.type === 'portal_root') {
        // 如果是portal根目录，检查portal目录是否存在
        physicalPath = pathContext.physicalPath;
      } else {
        // 对于其他情况，尝试在public uploads中查找
        physicalPath = path.join(PUBLIC_UPLOADS_DIR, filePath);
      }
    }
    
    // 检查文件是否存在
    const stats = await fsPromises.stat(physicalPath);
    const exists = stats.isFile();
    
    console.log(`[WS Server] 文件存在性验证: ${filePath} -> ${physicalPath} -> ${exists ? '存在' : '不存在'}`, {
      isPortalFile,
      physicalPath
    });
    return exists;
  } catch (error) {
    // 如果stat失败（文件不存在或权限问题），认为文件不存在
    console.log(`[WS Server] 文件存在性验证失败: ${filePath} -> 不存在 (${error.code})`);
    return false;
  }
}

// 同步状态到HTTP API的文件状态文件
async function syncToHttpApiStatus(filePath, status) {
  try {
    const httpStatusFile = path.join(process.cwd(), 'data', 'fileStatus.json');
    
    // 将中文状态值映射为HTTP API期望的英文值
    const statusMapping = {
      '未同步': 'pending',
      '同步中': 'pending', 
      '已同步': 'uploaded',
      '同步失败': 'pending'
    };
    
    const httpStatus = statusMapping[status] || 'pending';
    
    // 读取现有的HTTP状态数据
    let httpStatusData = {};
    if (fs.existsSync(httpStatusFile)) {
      try {
        const data = await fsPromises.readFile(httpStatusFile, 'utf8');
        httpStatusData = JSON.parse(data) || {};
      } catch (error) {
        console.error('[WS Server] 读取HTTP状态文件失败:', error);
      }
    }
    
    // 更新状态
    httpStatusData[filePath] = httpStatus;
    
    // 保存HTTP状态文件
    ensureDirectoryExists(path.dirname(httpStatusFile));
    await fsPromises.writeFile(httpStatusFile, JSON.stringify(httpStatusData, null, 2));
    
    console.log(`[WS Server] HTTP状态同步: ${filePath} -> ${httpStatus}`);
  } catch (error) {
    console.error('[WS Server] 同步HTTP状态失败:', error);
  }
}

// 获取文件状态
function getFileStatus(filePath) {
  const normalizedPath = filePath.replace(/\\/g, '/');
  return fileStatusCache[normalizedPath]?.status || FILE_STATUS.PENDING;
}

// 广播消息给所有客户端
function broadcastToClients(message) {
  const messageStr = JSON.stringify(message);
  connectedClients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(messageStr);
      } catch (error) {
        console.error('[WS Server] 发送广播消息失败:', error);
        connectedClients.delete(client);
      }
    }
  });
}

// 通知新文件检测
async function notifyNewFiles(newFiles) {
  if (newFiles.length === 0) return;
  
  console.log(`[WS Server] 检测到 ${newFiles.length} 个新文件，通知客户端`);
  
  // 将新文件状态设置为未同步
  for (const file of newFiles) {
    await updateFileStatus(file.path, FILE_STATUS.PENDING, {
      size: file.size,
      type: file.type,
      createdAt: file.createdAt
    });
  }
  
  // 广播新文件通知
  broadcastToClients({
    type: 'new_files_detected',
    files: newFiles,
    count: newFiles.length,
    timestamp: Date.now()
  });
}

// 移除文件状态
async function removeFileStatus(filePath, isFolder = false) {
  const normalizedPath = filePath.replace(/\\/g, '/');
  let removedCount = 0;
  
  // 删除指定路径的状态
  if (fileStatusCache[normalizedPath]) {
    delete fileStatusCache[normalizedPath];
    removedCount++;
    console.log(`[WS Server] 已删除文件状态: ${normalizedPath}`);
  }
  
  // 如果是文件夹，删除所有子路径的状态
  if (isFolder) {
    const folderPrefix = normalizedPath + '/';
    Object.keys(fileStatusCache).forEach(statusPath => {
      if (statusPath.startsWith(folderPrefix)) {
        delete fileStatusCache[statusPath];
        removedCount++;
        console.log(`[WS Server] 已删除子路径状态: ${statusPath}`);
      }
    });
  }
  
  if (removedCount > 0) {
    // 立即保存状态
    await saveFileStatus();
    
    // 同步到HTTP API状态文件
    await removeFromHttpApiStatus(normalizedPath, isFolder);
    
    console.log(`[WS Server] 总共删除了 ${removedCount} 个状态记录`);
  }
  
  return true;
}

// 从HTTP API状态文件中移除状态
async function removeFromHttpApiStatus(filePath, isFolder = false) {
  try {
    const httpStatusFile = path.join(process.cwd(), 'data', 'fileStatus.json');
    
    // 读取现有的HTTP状态数据
    let httpStatusData = {};
    if (fs.existsSync(httpStatusFile)) {
      try {
        const data = await fsPromises.readFile(httpStatusFile, 'utf8');
        httpStatusData = JSON.parse(data) || {};
      } catch (error) {
        console.error('[WS Server] 读取HTTP状态文件失败:', error);
        return;
      }
    }
    
    let removedCount = 0;
    
    // 删除指定路径的状态
    if (httpStatusData[filePath]) {
      delete httpStatusData[filePath];
      removedCount++;
    }
    
    // 如果是文件夹，删除所有子路径的状态
    if (isFolder) {
      const folderPrefix = filePath + '/';
      Object.keys(httpStatusData).forEach(statusPath => {
        if (statusPath.startsWith(folderPrefix)) {
          delete httpStatusData[statusPath];
          removedCount++;
        }
      });
    }
    
    if (removedCount > 0) {
      // 保存HTTP状态文件
      ensureDirectoryExists(path.dirname(httpStatusFile));
      await fsPromises.writeFile(httpStatusFile, JSON.stringify(httpStatusData, null, 2));
      console.log(`[WS Server] HTTP状态同步移除: ${filePath}, 移除了 ${removedCount} 个记录`);
    }
  } catch (error) {
    console.error('[WS Server] 同步移除HTTP状态失败:', error);
  }
}

// 执行状态一致性检查
async function performStatusConsistencyCheck() {
  try {
    console.log('[WS Server] 开始状态一致性检查...');
    let cleanedCount = 0;
    const filesToRemove = [];
    
    // 遍历所有已记录状态的文件
    for (const filePath of Object.keys(fileStatusCache)) {
      const fileExists = await verifyFileExists(filePath);
      
      if (!fileExists) {
        console.log(`[WS Server] 状态一致性检查：发现不存在的文件状态记录: ${filePath}`);
        filesToRemove.push(filePath);
      }
    }
    
    // 批量清理不存在文件的状态记录
    for (const filePath of filesToRemove) {
      const currentStatus = fileStatusCache[filePath]?.status;
      console.log(`[WS Server] 清理不存在文件的状态: ${filePath} (原状态: ${currentStatus})`);
      
      // 删除状态记录
      delete fileStatusCache[filePath];
      cleanedCount++;
      
      // 广播状态移除消息给所有客户端
      broadcastToClients({
        type: 'file_status_removed',
        filePath: filePath,
        isFolder: false,
        timestamp: Date.now(),
        reason: 'consistency_check'
      });
    }
    
    if (cleanedCount > 0) {
      // 保存更新后的状态缓存
      await saveFileStatus();
      
      // 同步清理HTTP API状态文件
      for (const filePath of filesToRemove) {
        await removeFromHttpApiStatus(filePath, false);
      }
      
      console.log(`[WS Server] 状态一致性检查完成，清理了 ${cleanedCount} 个无效状态记录`);
      
      // 通知客户端状态已清理
      broadcastToClients({
        type: 'status_consistency_check_completed',
        cleanedCount: cleanedCount,
        cleanedFiles: filesToRemove,
        timestamp: Date.now()
      });
    } else {
      console.log('[WS Server] 状态一致性检查完成，未发现无效状态记录');
    }
  } catch (error) {
    console.error('[WS Server] 状态一致性检查失败:', error);
  }
}
