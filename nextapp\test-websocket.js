// WebSocket服务器测试脚本
const WebSocket = require('ws');

console.log('测试WebSocket连接...');

// 测试本地连接
console.log('1. 测试本地连接 (localhost:6656)');
const wsLocal = new WebSocket('ws://localhost:6656');

wsLocal.on('open', () => {
    console.log('✅ 本地连接成功');
    wsLocal.close();
});

wsLocal.on('error', (error) => {
    console.log('❌ 本地连接失败:', error.message);
});

// 测试局域网连接
setTimeout(() => {
    console.log('2. 测试局域网连接 (***************:6656)');
    const wsNetwork = new WebSocket('ws://***************:6656');
    
    wsNetwork.on('open', () => {
        console.log('✅ 局域网连接成功');
        wsNetwork.close();
        process.exit(0);
    });
    
    wsNetwork.on('error', (error) => {
        console.log('❌ 局域网连接失败:', error.message);
        process.exit(1);
    });
}, 1000);

// 超时退出
setTimeout(() => {
    console.log('⏰ 测试超时');
    process.exit(1);
}, 5000);
