@echo off
echo 重启WebSocket服务器...

REM 查找并终止现有的WebSocket服务器进程
echo 查找现有WebSocket进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq node.exe" /fo csv ^| findstr "websocket-server"') do (
    echo 终止进程 %%i
    taskkill /pid %%i /f
)

REM 等待进程完全终止
timeout /t 2 /nobreak >nul

REM 启动新的WebSocket服务器
echo 启动WebSocket服务器...
start /b node websocket-server.js > logs\websocket.log 2>&1

REM 等待服务器启动
timeout /t 3 /nobreak >nul

REM 测试连接
echo 测试WebSocket连接...
node test-websocket.js

echo 完成！
