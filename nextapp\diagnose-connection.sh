#!/bin/bash

# 文件云流转应用连接诊断脚本

echo "=== 文件云流转应用连接诊断 ==="
echo "时间: $(date)"
echo ""

# 1. 检查进程状态
echo "1. 检查进程状态:"
echo "-------------------"

if [ -f "app.pid" ]; then
    PIDS=$(cat app.pid)
    echo "PID文件内容: $PIDS"
    
    for PID in $PIDS; do
        if ps -p $PID > /dev/null 2>&1; then
            PROCESS_INFO=$(ps -p $PID -o pid,ppid,cmd --no-headers)
            echo "✅ 进程 $PID 正在运行: $PROCESS_INFO"
        else
            echo "❌ 进程 $PID 未运行"
        fi
    done
else
    echo "❌ 未找到 app.pid 文件"
fi

echo ""

# 2. 检查端口占用
echo "2. 检查端口占用:"
echo "-------------------"

# 检查主服务器端口 (默认3000)
MAIN_PORT=3000
if netstat -tuln 2>/dev/null | grep ":$MAIN_PORT " > /dev/null; then
    echo "✅ 主服务器端口 $MAIN_PORT 已监听"
    netstat -tuln | grep ":$MAIN_PORT "
else
    echo "❌ 主服务器端口 $MAIN_PORT 未监听"
fi

# 检查WebSocket端口 (6656)
WS_PORT=6656
if netstat -tuln 2>/dev/null | grep ":$WS_PORT " > /dev/null; then
    echo "✅ WebSocket端口 $WS_PORT 已监听"
    netstat -tuln | grep ":$WS_PORT "
else
    echo "❌ WebSocket端口 $WS_PORT 未监听"
fi

echo ""

# 3. 测试HTTP连接
echo "3. 测试HTTP连接:"
echo "-------------------"

# 测试主服务器
if curl -s -o /dev/null -w "%{http_code}" http://localhost:$MAIN_PORT > /tmp/http_test 2>/dev/null; then
    HTTP_CODE=$(cat /tmp/http_test)
    if [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "404" ]; then
        echo "✅ 主服务器HTTP连接正常 (状态码: $HTTP_CODE)"
    else
        echo "⚠️  主服务器HTTP连接异常 (状态码: $HTTP_CODE)"
    fi
else
    echo "❌ 主服务器HTTP连接失败"
fi

# 测试API端点
if curl -s -o /dev/null -w "%{http_code}" http://localhost:$MAIN_PORT/api/admin/files/status > /tmp/api_test 2>/dev/null; then
    API_CODE=$(cat /tmp/api_test)
    echo "API端点 /api/admin/files/status 状态码: $API_CODE"
    if [ "$API_CODE" = "405" ]; then
        echo "✅ API端点存在 (405 = Method Not Allowed, 正常)"
    elif [ "$API_CODE" = "404" ]; then
        echo "❌ API端点不存在 (404 Not Found)"
    else
        echo "⚠️  API端点状态异常 (状态码: $API_CODE)"
    fi
else
    echo "❌ API端点连接失败"
fi

echo ""

# 4. 测试WebSocket连接
echo "4. 测试WebSocket连接:"
echo "-------------------"

# 使用curl测试WebSocket握手
if curl -s -o /dev/null -w "%{http_code}" \
    -H "Connection: Upgrade" \
    -H "Upgrade: websocket" \
    -H "Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==" \
    -H "Sec-WebSocket-Version: 13" \
    http://localhost:$WS_PORT > /tmp/ws_test 2>/dev/null; then
    WS_CODE=$(cat /tmp/ws_test)
    if [ "$WS_CODE" = "101" ]; then
        echo "✅ WebSocket握手成功 (状态码: 101)"
    elif [ "$WS_CODE" = "404" ]; then
        echo "❌ WebSocket服务器返回404，可能未正确启动"
    else
        echo "⚠️  WebSocket握手异常 (状态码: $WS_CODE)"
    fi
else
    echo "❌ WebSocket连接失败"
fi

echo ""

# 5. 检查日志文件
echo "5. 检查日志文件:"
echo "-------------------"

if [ -d "logs" ]; then
    echo "日志目录存在:"
    ls -la logs/
    echo ""
    
    # 显示最近的错误日志
    if [ -f "logs/server.log" ]; then
        echo "主服务器最近日志 (最后10行):"
        tail -10 logs/server.log
        echo ""
    fi
    
    if [ -f "logs/websocket.log" ]; then
        echo "WebSocket服务器最近日志 (最后10行):"
        tail -10 logs/websocket.log
        echo ""
    fi
    
    if [ -f "logs/startup.log" ]; then
        echo "启动日志:"
        cat logs/startup.log
        echo ""
    fi
else
    echo "❌ 日志目录不存在"
fi

# 6. 检查关键文件
echo "6. 检查关键文件:"
echo "-------------------"

FILES_TO_CHECK=("server.js" "websocket-server.js" "pages/api/admin/files/status.js")

for FILE in "${FILES_TO_CHECK[@]}"; do
    if [ -f "$FILE" ]; then
        echo "✅ $FILE 存在"
    else
        echo "❌ $FILE 不存在"
    fi
done

echo ""

# 7. 建议修复步骤
echo "7. 建议修复步骤:"
echo "-------------------"

if ! netstat -tuln 2>/dev/null | grep ":$WS_PORT " > /dev/null; then
    echo "❌ WebSocket服务器未运行，建议:"
    echo "   1. 停止现有服务: ./stop-background.sh"
    echo "   2. 重新启动: ./start-background.sh"
    echo "   3. 检查日志: tail -f logs/websocket.log"
fi

if ! curl -s -o /dev/null http://localhost:$MAIN_PORT 2>/dev/null; then
    echo "❌ 主服务器未运行，建议:"
    echo "   1. 检查端口是否被占用: netstat -tuln | grep :$MAIN_PORT"
    echo "   2. 重新启动服务: ./start-background.sh"
    echo "   3. 检查日志: tail -f logs/server.log"
fi

echo ""
echo "=== 诊断完成 ==="

# 清理临时文件
rm -f /tmp/http_test /tmp/api_test /tmp/ws_test
