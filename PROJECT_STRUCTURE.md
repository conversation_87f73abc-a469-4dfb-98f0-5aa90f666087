# 文件云流转系统 - 项目结构总览

## 📁 项目组织结构

本项目已重新组织，将Chrome扩展相关文件整理到专门的文件夹中，便于管理和部署。

```
ylz/                                 # 项目根目录
├── chrome-extension/                # 🔌 Chrome扩展插件（已整理）
│   ├── README.md                    # 插件说明文档
│   ├── INSTALL.md                   # 安装指南
│   ├── manifest.json                # 扩展配置清单
│   ├── popup.html                   # 弹出窗口界面
│   ├── popup.js                     # 弹出窗口逻辑
│   ├── background.js                # 后台服务工作者
│   ├── content.js                   # 通用内容脚本
│   ├── content-script-yunpan.js     # 云盘专用脚本
│   ├── config.js                    # 配置管理
│   ├── preload.js                   # 预加载脚本
│   ├── icons/                       # 图标资源
│   ├── styles/                      # 样式文件
│   ├── components/                  # UI组件
│   ├── utils/                       # 工具函数
│   ├── backups/                     # 备份文件
│   └── tests-and-debug/             # 测试和调试工具
├── nextapp/                         # 🌐 Next.js后端服务
│   ├── package.json                 # Node.js依赖配置
│   ├── next.config.js               # Next.js配置
│   ├── server.js                    # HTTP服务器
│   ├── websocket-server.js          # WebSocket服务器
│   ├── pages/                       # 页面路由
│   ├── components/                  # React组件
│   ├── public/                      # 静态资源
│   ├── uploads/                     # 文件上传目录
│   ├── data/                        # 数据存储
│   ├── ssl/                         # SSL证书
│   └── scripts/                     # 脚本工具
├── data/                            # 📊 数据存储
│   ├── file-status/                 # 文件状态数据
│   └── upload-portals/              # 上传门户配置
├── public/                          # 🌍 公共资源
│   └── uploads/                     # 公共上传文件
├── src/                             # 📝 源代码（其他）
│   ├── app.js                       # 应用主文件
│   └── utils/                       # 工具函数
├── node_modules/                    # 📦 Node.js依赖包
├── package.json                     # 项目依赖配置
├── package-lock.json               # 依赖锁定文件
├── 插件全面分析报告.md               # 📋 项目分析报告
└── PROJECT_STRUCTURE.md            # 📁 本文件（项目结构说明）
```

## 🎯 主要组件说明

### 1. Chrome扩展插件 (`chrome-extension/`)
**功能**: 浏览器端的用户界面和自动化脚本
**技术**: Manifest V3, 原生JavaScript, HTML, CSS
**部署**: 通过Chrome开发者模式加载

**核心文件**:
- `manifest.json` - 扩展配置和权限声明
- `background.js` - 后台服务工作者，处理WebSocket通信
- `popup.js` - 用户界面逻辑，文件管理和状态显示
- `content-script-yunpan.js` - 云盘页面自动化脚本

### 2. Next.js后端服务 (`nextapp/`)
**功能**: 提供HTTP API和WebSocket服务
**技术**: Next.js, Node.js, WebSocket
**部署**: 生产环境服务器部署

**核心文件**:
- `server.js` - HTTP服务器，处理文件上传和API请求
- `websocket-server.js` - WebSocket服务器，实时通信
- `pages/api/` - API路由处理
- `pages/portal/` - 门户页面

### 3. 数据存储 (`data/`)
**功能**: 存储文件状态、配置信息等
**格式**: JSON文件
**用途**: 状态同步、配置管理

## 🚀 部署指南

### Chrome扩展部署
1. 进入 `chrome-extension/` 文件夹
2. 参考 `INSTALL.md` 进行安装
3. 在Chrome中加载扩展

### 后端服务部署
1. 进入 `nextapp/` 文件夹
2. 运行 `npm install` 安装依赖
3. 运行 `npm run start:bg` 启动后台服务

### 开发环境启动
```bash
# 启动后端服务（开发模式）
cd nextapp
npm run dev

# 加载Chrome扩展
# 在Chrome中访问 chrome://extensions/
# 启用开发者模式，加载 chrome-extension 文件夹
```

## 🔧 配置说明

### 网络配置
- **HTTP服务**: `http://192.168.100.110:3000`
- **WebSocket服务**: `ws://192.168.100.110:6656`
- **目标云盘**: `https://yunpan.gdcourts.gov.cn`

### 文件路径配置
- **上传目录**: `nextapp/uploads/`
- **公共资源**: `public/uploads/`
- **数据存储**: `data/`

## 📊 技术架构

### 通信流程
```
Chrome扩展 ←→ WebSocket服务器 ←→ 文件系统
     ↓              ↓              ↓
  用户界面    ←→  HTTP API  ←→   数据存储
     ↓              ↓              ↓
  自动化脚本  ←→  云盘网站  ←→   文件上传
```

### 数据流向
1. **文件检测**: Chrome扩展监控文件变化
2. **状态同步**: WebSocket实时更新文件状态
3. **自动上传**: 内容脚本执行云盘上传操作
4. **结果反馈**: 更新用户界面显示结果

## 🛠️ 开发工具

### 调试工具
位于 `chrome-extension/tests-and-debug/`:
- WebSocket连接测试
- 上传功能调试
- 状态同步诊断

### 日志系统
- **Chrome扩展**: 浏览器控制台
- **后端服务**: `nextapp/logs/`
- **WebSocket**: 实时日志输出

## 📝 维护说明

### 备份策略
- **代码备份**: `chrome-extension/backups/`
- **数据备份**: 定期备份 `data/` 目录
- **配置备份**: 保存关键配置文件

### 更新流程
1. **扩展更新**: 替换文件后重新加载扩展
2. **服务更新**: 重启后端服务
3. **数据迁移**: 必要时更新数据格式

### 监控要点
- WebSocket连接状态
- 文件上传成功率
- 系统资源使用情况
- 错误日志分析

---

## 📞 技术支持

如需技术支持或有疑问，请：
1. 查看相关文档（README.md, INSTALL.md）
2. 检查日志文件获取错误信息
3. 使用调试工具进行问题诊断
4. 联系开发团队获取帮助

**项目状态**: 生产就绪  
**最后更新**: 2025年1月  
**维护团队**: 文件云流转开发组
