# 文件云流转助手 - Chrome扩展

## 📋 项目概述

文件云流转助手是一个专门为政府云盘系统设计的Chrome浏览器扩展，实现了自动化文件上传和管理功能。

**版本**: 1.0  
**目标平台**: 政府云盘系统 (yunpan.gdcourts.gov.cn)  
**技术标准**: Chrome Extension Manifest V3  

## 📁 文件结构

```
chrome-extension/
├── manifest.json                    # 扩展配置清单
├── popup.html                       # 弹出窗口界面
├── popup.js                         # 弹出窗口逻辑 (2,664行)
├── background.js                    # 后台服务工作者 (3,354行)
├── content.js                       # 通用内容脚本 (2,427行)
├── content-script-yunpan.js         # 云盘专用脚本 (3,554行)
├── content-script-injected-panel.js # 页面注入面板脚本 🆕
├── injected-panel.html              # 注入面板HTML模板 🆕
├── injected-panel.js                # 注入面板逻辑 🆕
├── injected-panel-tree.js           # 注入面板文件树组件 🆕
├── config.js                        # 配置管理 (706行)
├── preload.js                       # 预加载脚本
├── icons/                           # 图标资源
│   ├── icon16.png                   # 16x16 图标
│   ├── icon48.png                   # 48x48 图标
│   ├── icon128.png                  # 128x128 图标
│   └── file-types/                  # 文件类型图标
├── styles/                          # 样式文件
│   ├── popup.css                    # 主样式文件 (1,519行)
│   └── tree-view.css                # 树形视图样式 (537行)
├── components/                      # UI组件
│   ├── LazyThumbnail.js             # 懒加载缩略图组件 (567行)
│   └── PreviewModal.js              # 预览模态框组件 (937行)
├── utils/                           # 工具函数
│   ├── thumbnail-cache.js           # 缩略图缓存系统 (613行)
│   └── file-icon-mapper.js          # 文件图标映射 (298行)
└── 测试和调试文件/
    ├── extension_websocket_test.html
    ├── websocket_diagnostic.html
    ├── websocket_monitor.html
    ├── test_yunpan_upload.html
    ├── simple_upload_test.html
    ├── upload_debug_tool.html
    ├── test_yunpan_debug.js
    └── 文件云流转.html
```

## 🚀 核心功能

### 1. 文件监控与管理
- 实时监控文件变化
- 自动分类待上传/已上传文件
- 文件状态跟踪和同步

### 2. 云盘自动化
- 自动创建日期文件夹
- 智能文件夹导航
- 批量文件上传处理

### 3. 用户界面
- 现代化卡片式设计
- 文件缩略图预览
- 响应式布局设计

### 4. 页面注入面板 🆕
- 与popup完全一致的UI布局和功能
- 专门针对 `https://yunpan.gdcourts.gov.cn:82/` 网站
- 可拖拽、最小化、关闭的浮动面板
- 实时文件状态同步和管理
- 半透明背景和现代化设计

### 5. 缓存系统
- 三层缓存架构
- 懒加载优化
- 性能优化机制

## 🔧 技术架构

### 核心组件
- **Background Service Worker**: 后台服务和WebSocket通信
- **Popup Interface**: 用户交互界面
- **Injected Panel**: 页面注入面板，与popup功能一致 🆕
- **Content Scripts**: 页面DOM操作和自动化
- **Caching System**: 智能缓存和性能优化

### 通信机制
- WebSocket实时通信 (端口: 6656)
- Chrome消息传递API
- HTTP REST API调用

## 📦 安装和使用

### 开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 启用"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择此文件夹

### 配置要求
- 后端服务器地址: ***************
- WebSocket端口: 6656
- HTTP服务端口: 3000

### 页面注入面板使用 🆕
1. 安装扩展后，访问 `https://yunpan.gdcourts.gov.cn:82/`
2. 页面加载完成后，右上角会自动显示注入面板
3. 面板功能与popup完全一致，支持：
   - 拖拽移动到任意位置
   - 最小化和关闭操作
   - 实时文件状态管理
   - 批量上传和自动上传
4. 详细使用说明请参考 `INJECTED_PANEL_GUIDE.md`

## 🛠️ 开发说明

### 主要文件说明
- `manifest.json`: 扩展配置，定义权限和资源
- `background.js`: 后台服务，处理WebSocket通信和文件管理
- `popup.js`: 弹出窗口逻辑，用户界面交互
- `content-script-yunpan.js`: 云盘页面自动化脚本
- `content.js`: 通用内容脚本，处理页面监控

### 调试工具
项目包含多个调试和测试文件：
- WebSocket连接测试
- 文件上传调试工具
- 状态同步诊断工具

## 📊 代码统计

- **总代码量**: ~13,600行
- **JavaScript**: ~11,000行
- **CSS**: ~2,000行
- **HTML**: ~600行

## 🔍 技术特点

- **Manifest V3**: 使用最新的Chrome扩展标准
- **模块化设计**: 清晰的代码结构和职责分离
- **现代化UI**: 响应式设计和动画效果
- **强健错误处理**: 完善的异常捕获和恢复机制
- **性能优化**: 缓存机制和懒加载技术

## 📝 维护说明

### 备份文件
项目包含以下备份文件：
- `manifest.json.backup`
- `popup.js.backup`
- `background-original.js`
- `popup-original.js`
- `content-script-yunpan.js.backup`

### 日志和调试
- 详细的控制台日志输出
- WebSocket连接状态监控
- 文件操作状态跟踪

## 🚦 注意事项

1. **网络依赖**: 需要连接到指定的后端服务器
2. **权限要求**: 需要访问所有网站的权限
3. **浏览器兼容**: 仅支持Chrome浏览器
4. **安全考虑**: 在政府环境中使用需要安全审查

## 📞 技术支持

如需技术支持或有问题反馈，请联系开发团队。

---

**最后更新**: 2025年1月
**开发状态**: 生产就绪
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 