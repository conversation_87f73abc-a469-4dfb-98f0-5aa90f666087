'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import Link from 'next/link'
import path from 'path'

export default function FilesPage() {
  const [files, setFiles] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploading, setUploading] = useState(false)
  const [currentPath, setCurrentPath] = useState('/')
  const [pathHistory, setPathHistory] = useState([{ name: '根目录', path: '/' }])
  const [showNewFolderModal, setShowNewFolderModal] = useState(false)
  const [newFolderName, setNewFolderName] = useState('')
  const [creatingFolder, setCreatingFolder] = useState(false)
  const [previewFile, setPreviewFile] = useState(null)
  const [showPreviewModal, setShowPreviewModal] = useState(false)
  const [toast, setToast] = useState(null)
  const [toastVisible, setToastVisible] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef(null)
  const folderInputRef = useRef(null)
  const dropZoneRef = useRef(null)
  const [folders, setFolders] = useState([])
  const [totalSize, setTotalSize] = useState(0)
  const [totalFiles, setTotalFiles] = useState(0)
  const [portals, setPortals] = useState([])
  const [currentPortal, setCurrentPortal] = useState(null)
  const [syncStatuses, setSyncStatuses] = useState({})
  const [wsConnected, setWsConnected] = useState(false)
  const [wsRef, setWsRef] = useState(null)

  useEffect(() => {
    loadPortalData()
  }, [currentPath])

  useEffect(() => {
    if (toast) {
      setToastVisible(true)
      const timer = setTimeout(() => {
        setToastVisible(false)
        setTimeout(() => {
          setToast(null)
        }, 300) // 淡出动画后再清除toast数据
      }, 3000)
      
      return () => {
        clearTimeout(timer)
      }
    }
  }, [toast])

  useEffect(() => {
    const handlePaste = (e) => {
      if (e.clipboardData.files.length > 0) {
        e.preventDefault();
        handlePastedFiles(e.clipboardData.files);
      }
    };
    
    window.addEventListener('paste', handlePaste);
    
    return () => {
      window.removeEventListener('paste', handlePaste);
    };
  }, [currentPath]);

  useEffect(() => {
    const dropZone = dropZoneRef.current;
    if (!dropZone) return;
    
    const handleDragOver = (e) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(true);
    };
    
    const handleDragEnter = (e) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(true);
    };
    
    const handleDragLeave = (e) => {
      e.preventDefault();
      e.stopPropagation();
      if (!dropZone.contains(e.relatedTarget)) {
        setIsDragging(false);
      }
    };
    
    const handleDrop = (e) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);
      
      // 处理拖放的文件
      if (e.dataTransfer.files.length > 0) {
        // 检查是否有文件夹
        const items = e.dataTransfer.items;
        if (items && items.length > 0 && items[0].webkitGetAsEntry) {
          // 检查是否有文件夹
          const entry = items[0].webkitGetAsEntry();
          if (entry && entry.isDirectory) {
            // 处理文件夹上传
            handleDroppedFolder(items);
            return;
          }
        }
        
        handleDroppedFiles(e.dataTransfer.files);
      }
    };
    
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragenter', handleDragEnter);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    
    return () => {
      dropZone.removeEventListener('dragover', handleDragOver);
      dropZone.removeEventListener('dragenter', handleDragEnter);
      dropZone.removeEventListener('dragleave', handleDragLeave);
      dropZone.removeEventListener('drop', handleDrop);
    };
  }, [dropZoneRef.current, currentPath]);

  useEffect(() => {
    // 实现真正的WebSocket连接和消息处理
    console.log("正在初始化WebSocket连接...");
    
    let ws = null;
    let reconnectTimer = null;
    let isManualClose = false;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 10;

    const connectWebSocket = () => {
      try {
        // 连接到WebSocket服务器 - 使用与Chrome扩展相同的地址
        ws = new WebSocket('ws://192.168.100.110:6656');
        setWsRef(ws); // 保存WebSocket引用
        
        ws.onopen = () => {
          console.log('WebSocket连接已建立');
          setWsConnected(true);
          reconnectAttempts = 0; // 重置重连计数
          
          // 发送初始化消息
          ws.send(JSON.stringify({
            type: 'init',
            client: 'file-manager',
            version: '1.0.0'
          }));
          
          // 清除重连定时器
          if (reconnectTimer) {
            clearTimeout(reconnectTimer);
            reconnectTimer = null;
          }
          
          showToast('已连接到服务器', 'success');
        };

        ws.onmessage = (event) => {
          try {
            const messageData = JSON.parse(event.data);
            console.log('收到WebSocket消息:', messageData);
            
            // 处理不同类型的消息
            switch (messageData.type) {
              case 'file_status_updated':
                // 文件状态更新
                if (messageData.filePath && messageData.status) {
                  console.log(`文件状态更新: ${messageData.filePath} -> ${messageData.status}`);
                  console.log('当前syncStatuses状态:', syncStatuses);
                  
                  setSyncStatuses(prevStatuses => {
                    const newStatuses = {
                      ...prevStatuses,
                      [messageData.filePath]: messageData.status,
                    };
                    console.log('WebSocket更新后的状态:', newStatuses);
                    return newStatuses;
                  });
                  
                  // 显示状态更新提示
                  const fileName = messageData.filePath.split('/').pop();
                  const statusText = messageData.status === '已同步' ? '已同步' : 
                                   messageData.status === '同步中' ? '同步中' : 
                                   messageData.status === '同步失败' ? '同步失败' : '未同步';
                  showToast(`文件 ${fileName} 状态已更新为: ${statusText}`, 
                           messageData.status === '已同步' ? 'success' : 
                           messageData.status === '同步失败' ? 'error' : 'info');
                } else {
                  console.warn('收到无效的file_status_updated消息:', messageData);
                }
                break;
                
              case 'file_status_removed':
                // 文件状态移除
                if (messageData.filePath) {
                  console.log(`文件状态移除: ${messageData.filePath}${messageData.isFolder ? ' (文件夹)' : ''}`);
                  
                  setSyncStatuses(prevStatuses => {
                    const newStatuses = { ...prevStatuses };
                    
                    // 删除指定路径的状态
                    delete newStatuses[messageData.filePath];
                    
                    // 如果是文件夹，删除所有子路径的状态
                    if (messageData.isFolder) {
                      const folderPrefix = messageData.filePath + '/';
                      Object.keys(newStatuses).forEach(statusPath => {
                        if (statusPath.startsWith(folderPrefix)) {
                          delete newStatuses[statusPath];
                        }
                      });
                    }
                    
                    console.log('WebSocket移除状态后:', newStatuses);
                    return newStatuses;
                  });
                  
                  // 显示状态移除提示
                  const fileName = messageData.filePath.split('/').pop();
                  showToast(`已清理 ${fileName} 的状态记录`, 'info');
                } else {
                  console.warn('收到无效的file_status_removed消息:', messageData);
                }
                break;
                
              case 'admin_file_tree':
                // 文件树更新
                console.log('收到文件树更新，重新加载数据');
                loadPortalData();
                break;
                
              case 'new_files_detected':
                // 检测到新文件
                if (messageData.count > 0) {
                  console.log(`检测到 ${messageData.count} 个新文件`);
                  showToast(`检测到 ${messageData.count} 个新文件`, 'info');
                  // 重新加载文件数据
                  setTimeout(() => {
                    loadPortalData();
                  }, 1000);
                }
                break;
                
              case 'init_response':
                console.log('WebSocket初始化完成:', messageData.message);
                break;
                
              case 'pong':
                console.log('收到WebSocket心跳响应');
                break;
                
              case 'error':
                console.error('WebSocket服务器错误:', messageData.message);
                showToast(`服务器错误: ${messageData.message}`, 'error');
                break;
                
              default:
                console.log('未处理的WebSocket消息类型:', messageData.type);
            }
          } catch (error) {
            console.error('处理WebSocket消息时出错:', error);
          }
        };

        ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error);
          setWsConnected(false);
          showToast('WebSocket连接错误', 'error');
        };

        ws.onclose = (event) => {
          console.log('WebSocket连接已关闭:', event.code, event.reason);
          setWsConnected(false);
          setWsRef(null);
          
          // 如果不是手动关闭且未达到最大重连次数，尝试重连
          if (!isManualClose && reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // 指数退避，最大30秒
            
            showToast(`连接断开，${delay/1000}秒后尝试第${reconnectAttempts}次重连...`, 'warning');
            reconnectTimer = setTimeout(() => {
              console.log(`尝试第${reconnectAttempts}次重新连接WebSocket...`);
              connectWebSocket();
            }, delay);
          } else if (reconnectAttempts >= maxReconnectAttempts) {
            showToast('已达到最大重连次数，请手动刷新页面', 'error');
          }
        };
        
      } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        setWsConnected(false);
        showToast('无法连接到服务器', 'error');
        
        // 如果连接失败，也尝试重连
        if (!isManualClose && reconnectAttempts < maxReconnectAttempts) {
          reconnectAttempts++;
          const delay = Math.min(5000 * reconnectAttempts, 30000);
          reconnectTimer = setTimeout(() => {
            connectWebSocket();
          }, delay);
        }
      }
    };

    // 初始连接
    connectWebSocket();

    // 设置心跳检测
    const heartbeatInterval = setInterval(() => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }));
      }
    }, 30000); // 每30秒发送一次心跳

    // 清理函数
    return () => {
      console.log("WebSocket清理函数执行");
      isManualClose = true;
      
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
      }
      
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
      }
      
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
      
      setWsConnected(false);
      setWsRef(null);
    };
  }, []);

  // 发送WebSocket消息的函数
  const sendWebSocketMessage = (message) => {
    if (wsRef && wsRef.readyState === WebSocket.OPEN) {
      try {
        wsRef.send(JSON.stringify(message));
        console.log('发送WebSocket消息:', message);
        return true;
      } catch (error) {
        console.error('发送WebSocket消息失败:', error);
        return false;
      }
    } else {
      console.warn('WebSocket未连接，无法发送消息:', message);
      return false;
    }
  };

  const showToast = (message, type = 'success') => {
    if (toast) {
      setToastVisible(false)
      setTimeout(() => {
        setToast({ message, type })
      }, 300)
    } else {
      setToast({ message, type })
    }
  }

  const handlePastedFiles = (files) => {
    uploadFiles(files);
  };
  
  const handleDroppedFiles = (files) => {
    uploadFiles(files);
  };
  
  const uploadFiles = async (files) => {
    if (!files || files.length === 0) return;
    
    const formData = new FormData();
    formData.append('currentPath', currentPath);
    
    for (let i = 0; i < files.length; i++) {
      formData.append('file', files[i]);
    }
    
    try {
      setUploading(true);
      setUploadProgress(0);
      
      showToast('开始上传文件...', 'info');
      
      const response = await fetch('/api/admin/files', {
        method: 'POST',
        body: formData,
      });
      
      const data = await response.json();
      
      if (response.ok) {
        showToast(`成功上传 ${files.length} 个文件!`);
        
        // 先立即更新本地状态，避免被后续的loadPortalData覆盖
        const uploadedFilePaths = [];
        
        if (data.files && Array.isArray(data.files)) {
          // 遍历上传成功的文件，收集文件路径
          for (const uploadedFile of data.files) {
            const filePath = uploadedFile.path || uploadedFile.filename;
            uploadedFilePaths.push(filePath);
          }
        } else {
          // 如果没有返回具体文件信息，根据上传的文件名推断
          for (let i = 0; i < files.length; i++) {
            const fileName = files[i].name;
            const filePath = currentPath === '/' ? fileName : `${currentPath}/${fileName}`;
            uploadedFilePaths.push(filePath);
          }
        }
        
        // 立即在本地状态中设置这些文件为"未同步"
        setSyncStatuses(prevStatuses => {
          const newStatuses = { ...prevStatuses };
          uploadedFilePaths.forEach(filePath => {
            newStatuses[filePath] = '未同步';
          });
          console.log('上传后立即设置本地状态:', newStatuses);
          return newStatuses;
        });
        
        // 批量更新后端状态
        const statusUpdatePromises = uploadedFilePaths.map(async (filePath) => {
          try {
            await updateFileStatusToUnsynced(filePath);
          } catch (statusError) {
            console.error('更新文件状态失败:', filePath, statusError);
          }
        });
        
        // 等待所有状态更新完成
        await Promise.allSettled(statusUpdatePromises);
        
        // 给状态更新一些时间传播到后端
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 最后刷新文件列表（这时应该保留我们设置的状态）
        await loadPortalData();
      } else {
        showToast('上传失败: ' + (data.error || '未知错误'), 'error');
      }
    } catch (err) {
      showToast('上传错误: ' + err.message, 'error');
    } finally {
      setUploading(false);
      setUploadProgress(0);
      
      // 清除文件选择器的选择内容（拖拽上传后的清理）
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // 添加更新文件状态为已同步的函数（仅用于真正同步完成时）
  const markFileAsSynced = async (filePath) => {
    try {
      console.log(`标记文件为已同步: ${filePath}`);
      
      // 立即更新本地状态显示 - 使用filePath作为键
      setSyncStatuses(prevStatuses => {
        console.log('更新前的状态:', prevStatuses);
        const newStatuses = {
          ...prevStatuses,
          [filePath]: '已同步',
        };
        console.log('更新后的状态:', newStatuses);
        return newStatuses;
      });
      
      // 优先使用WebSocket更新状态
      if (wsConnected) {
        const success = sendWebSocketMessage({
          type: 'update_file_status',
          filename: filePath,
          status: '已同步', // 使用WebSocket服务器的中文状态值
          timestamp: Date.now()
        });
        
        if (success) {
          console.log(`通过WebSocket更新文件状态: ${filePath} -> 已同步`);
          showToast(`文件 ${filePath.split('/').pop()} 已标记为已同步`, 'success');
          return;
        } else {
          console.warn('WebSocket发送失败，降级到HTTP API');
        }
      }
      
      // 备用方案：通过fetch API调用状态更新接口
      const response = await fetch('/api/admin/files/status', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filePath: filePath,
          status: 'uploaded' // 后端使用的状态值
        }),
      });
      
      if (response.ok) {
        console.log(`通过HTTP API更新文件状态成功: ${filePath} -> 已同步`);
        showToast(`文件 ${filePath.split('/').pop()} 已标记为已同步`, 'success');
      } else {
        const errorData = await response.json();
        console.error('状态更新API调用失败:', errorData);
        // 如果API调用失败，回滚本地状态
        setSyncStatuses(prevStatuses => ({
          ...prevStatuses,
          [filePath]: '未同步',
        }));
        showToast(`更新 ${filePath.split('/').pop()} 状态失败`, 'error');
      }
    } catch (error) {
      console.error('更新文件状态时出错:', error);
      // 如果出错，回滚本地状态
      setSyncStatuses(prevStatuses => ({
        ...prevStatuses,
        [filePath]: '未同步',
      }));
      showToast(`更新 ${filePath.split('/').pop()} 状态失败`, 'error');
    }
  };

  // 添加更新文件状态为未同步的函数（新上传的文件）
  const updateFileStatusToUnsynced = async (filePath) => {
    try {
      console.log(`正在将新上传文件状态设置为未同步: ${filePath}`);
      
      // === 第一步：立即更新本地状态显示为"未同步" ===
      setSyncStatuses(prevStatuses => {
        console.log('状态更新前:', prevStatuses);
        const newStatuses = {
          ...prevStatuses,
          [filePath]: '未同步', // 新上传的文件必须是未同步状态
        };
        console.log('状态更新后:', newStatuses);
        return newStatuses;
      });
      
      // === 第二步：强制清理可能存在的旧状态（多重清理） ===
      console.log(`[新上传] 开始强制清理旧状态: ${filePath}`);
      
      // 2.1 通过WebSocket发送删除旧状态的请求
      if (wsConnected) {
        const cleanupSuccess = sendWebSocketMessage({
          type: 'remove_file_status',
          filename: filePath,
          isFolder: false,
          timestamp: Date.now(),
          reason: 'pre_upload_cleanup'
        });
        
        if (cleanupSuccess) {
          console.log(`[新上传] 已发送状态清理请求: ${filePath}`);
          // 给状态清理足够的时间
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      // 2.2 再次发送强制清理请求（确保清理彻底）
      if (wsConnected) {
        const secondCleanupSuccess = sendWebSocketMessage({
          type: 'remove_file_status',
          filename: filePath,
          isFolder: false,
          timestamp: Date.now(),
          reason: 'force_pre_upload_cleanup'
        });
        
        if (secondCleanupSuccess) {
          console.log(`[新上传] 已发送第二次状态清理请求: ${filePath}`);
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }
      
      // === 第三步：多重状态设置确保"未同步" ===
      let statusUpdateSuccess = false;
      
      // 3.1 第一次WebSocket状态设置
      if (wsConnected) {
        const success = sendWebSocketMessage({
          type: 'update_file_status',
          filename: filePath,
          status: '未同步',
          timestamp: Date.now(),
          forceUpdate: true,
          isNewUpload: true,
          reason: 'new_file_upload'
        });
        
        if (success) {
          console.log(`[新上传] 第一次WebSocket状态设置: ${filePath} -> 未同步`);
          statusUpdateSuccess = true;
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
      
      // 3.2 第二次WebSocket状态设置（双重保险）
      if (wsConnected) {
        const secondSuccess = sendWebSocketMessage({
          type: 'update_file_status',
          filename: filePath,
          status: '未同步',
          timestamp: Date.now(),
          forceUpdate: true,
          isNewUpload: true,
          reason: 'new_file_upload_confirmation'
        });
        
        if (secondSuccess) {
          console.log(`[新上传] 第二次WebSocket状态设置: ${filePath} -> 未同步`);
          statusUpdateSuccess = true;
        }
      }
      
      // === 第四步：HTTP API备用状态设置 ===
      if (!statusUpdateSuccess || !wsConnected) {
        console.log(`[新上传] 使用HTTP API设置状态: ${filePath}`);
        
        try {
          // 4.1 先尝试删除旧状态
          const deleteResponse = await fetch('/api/admin/files/status', {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              filePath: filePath,
              reason: 'new_upload_cleanup'
            }),
          });
          
          if (deleteResponse.ok) {
            console.log(`[新上传] HTTP API删除旧状态成功: ${filePath}`);
          }
          
          // 4.2 设置新的"未同步"状态
          const response = await fetch('/api/admin/files/status', {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              filePath: filePath,
              status: 'pending',
              forceUpdate: true,
              isNewUpload: true,
              reason: 'new_file_upload'
            }),
          });
          
          if (response.ok) {
            console.log(`[新上传] HTTP API状态设置成功: ${filePath} -> 未同步`);
            statusUpdateSuccess = true;
          } else {
            const errorData = await response.json();
            console.error(`[新上传] HTTP API状态设置失败:`, errorData);
          }
        } catch (httpError) {
          console.error(`[新上传] HTTP API调用异常:`, httpError);
        }
      }
      
      // === 第五步：验证状态设置结果 ===
      if (statusUpdateSuccess) {
        // 5.1 再次确认本地状态
        setSyncStatuses(prevStatuses => ({
          ...prevStatuses,
          [filePath]: '未同步'
        }));
        
        // 5.2 显示成功消息
        const fileName = filePath.split('/').pop();
        console.log(`[新上传] 文件状态设置完成: ${fileName} -> 未同步`, {
          filePath: filePath,
          finalStatus: '未同步',
          method: wsConnected ? 'WebSocket' : 'HTTP API'
        });
        
        showToast(`文件 ${fileName} 已上传，状态：未同步`, 'success');
      } else {
        // 5.3 设置失败但保持未同步状态（安全的默认值）
        const fileName = filePath.split('/').pop();
        console.warn(`[新上传] 状态设置可能失败，但保持未同步状态: ${fileName}`);
        showToast(`文件 ${fileName} 已上传，请手动检查同步状态`, 'warning');
      }
      
    } catch (error) {
      console.error(`[新上传] 状态设置过程出错:`, error);
      
      // 即使出错，也确保本地显示为未同步状态
      setSyncStatuses(prevStatuses => ({
        ...prevStatuses,
        [filePath]: '未同步'
      }));
      
      const fileName = filePath.split('/').pop();
      showToast(`文件 ${fileName} 已上传，状态设置遇到问题，请手动检查`, 'warning');
    }
  };

  const fetchFiles = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/admin/files?path=${encodeURIComponent(currentPath)}`)
      const data = await response.json()
      
      if (response.ok) {
        setFiles(data.files || [])
      } else {
        setError(data.error || '获取文件列表失败')
      }
    } catch (err) {
      setError('网络错误: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (selectedFiles, filePaths = []) => {
    if (!selectedFiles || selectedFiles.length === 0) return
    
    setUploading(true)
    setUploadProgress(0)
    setError(null)
    
    try {
      const formData = new FormData()
      
      // 添加所有文件到FormData
      for (let i = 0; i < selectedFiles.length; i++) {
        formData.append('file', selectedFiles[i])
        
        // 如果有文件夹上传的路径，包含它们
        if (filePaths[i]) {
          formData.append('paths', filePaths[i])
        }
      }

      // 添加当前路径
      formData.append('currentPath', currentPath)
      
      // 如果当前路径以入口名称开头，添加入口ID
      const pathParts = currentPath.split('/')
      if (pathParts.length > 0) {
        const portalFolder = portals.find(f => f.isPortal && f.name === pathParts[0])
        if (portalFolder) {
          formData.append('portalId', portalFolder.portalId)
        }
      }

      // 发送上传请求
      const response = await fetch('/api/admin/files', {
        method: 'POST',
        body: formData,
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '上传失败')
      }
      
      showToast(`成功上传 ${selectedFiles.length} 个文件!`, 'success');
      
      // 准备文件路径列表
      const uploadedFilePaths = [];
      for (let i = 0; i < selectedFiles.length; i++) {
        const fileName = selectedFiles[i].name;
        let filePath;
        
        if (filePaths[i]) {
          // 如果有特定路径（文件夹上传）
          filePath = currentPath === '/' ? filePaths[i] : `${currentPath}/${filePaths[i]}`;
        } else {
          // 普通文件上传
          filePath = currentPath === '/' ? fileName : `${currentPath}/${fileName}`;
        }
        uploadedFilePaths.push(filePath);
      }
      
      // 立即在本地状态中设置这些文件为"未同步"
      setSyncStatuses(prevStatuses => {
        const newStatuses = { ...prevStatuses };
        uploadedFilePaths.forEach(filePath => {
          newStatuses[filePath] = '未同步';
        });
        console.log('handleFileUpload 后立即设置本地状态:', newStatuses);
        return newStatuses;
      });
      
      // === 记录上传时间戳，用于状态保护 ===
      const uploadTimestamp = Date.now();
      try {
        const timestamps = JSON.parse(localStorage.getItem('uploadTimestamps') || '{}');
        uploadedFilePaths.forEach(filePath => {
          timestamps[filePath] = uploadTimestamp;
        });
        localStorage.setItem('uploadTimestamps', JSON.stringify(timestamps));
        console.log('[上传时间戳] 已记录文件上传时间:', {
          files: uploadedFilePaths,
          timestamp: uploadTimestamp
        });
      } catch (error) {
        console.warn('[上传时间戳] 记录失败:', error);
      }
      
      // 批量更新后端状态
      const statusUpdatePromises = uploadedFilePaths.map(async (filePath) => {
        try {
          await updateFileStatusToUnsynced(filePath);
        } catch (statusError) {
          console.error('更新文件状态失败:', filePath, statusError);
        }
      });
      
      // 等待状态更新完成
      await Promise.allSettled(statusUpdatePromises);
      
      // 给状态传播一些时间
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 上传成功后刷新文件列表
      await loadPortalData()
      
      // 清除文件选择器的选择内容
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (err) {
      console.error('上传文件失败:', err)
      setError(err.message || '上传文件时发生错误')
      showToast('上传失败: ' + (err.message || '未知错误'), 'error');
    } finally {
      setUploading(false)
      setUploadProgress(100)
      // 重置进度条
      setTimeout(() => setUploadProgress(0), 1000)
    }
  }

  const handleFolderUpload = async (e) => {
    const fileInput = e.target;
    if (!fileInput.files || fileInput.files.length === 0) return;

    const formData = new FormData();
    
    // Add current path to potentially distinguish target location in backend
    formData.append('currentPath', currentPath); 
    // Add portalId if currently inside a portal
    if (currentPortal) {
        formData.append('portalId', currentPortal.id);
    }

    for (let i = 0; i < fileInput.files.length; i++) {
      const file = fileInput.files[i];
      if (!file) continue;
      
      // Pass the file and its relative path within the folder structure
      formData.append('file', file);
      // webkitRelativePath includes the top-level folder name selected
      formData.append('filePaths', file.webkitRelativePath || ''); 
    }
    
    try {
      setUploading(true);
      setUploadProgress(0);
      
      // Determine the correct API endpoint based on context
      const uploadUrl = currentPortal 
        ? `/api/portal/${currentPortal.id}/files/folder` // Portal specific endpoint (Needs Creation)
        : '/api/admin/files/folder'; // Existing admin endpoint

      const response = await fetch(uploadUrl, { // Use dynamic URL
        method: 'POST',
        body: formData,
        // Note: Do not set Content-Type header when sending FormData
      });
      
      const data = await response.json();
      
      if (response.ok) {
        showToast('文件夹上传成功!');
        
        // 准备文件路径列表
        const uploadedFilePaths = [];
        if (fileInput.files && fileInput.files.length > 0) {
          for (let i = 0; i < fileInput.files.length; i++) {
            const file = fileInput.files[i];
            if (!file) continue;
            
            // 构建文件路径
            const relativePath = file.webkitRelativePath || file.name;
            let filePath;
            
            if (currentPath === '/') {
              filePath = relativePath;
            } else {
              filePath = `${currentPath}/${relativePath}`;
            }
            uploadedFilePaths.push(filePath);
          }
        }
        
        // 立即在本地状态中设置这些文件为"未同步"
        setSyncStatuses(prevStatuses => {
          const newStatuses = { ...prevStatuses };
          uploadedFilePaths.forEach(filePath => {
            newStatuses[filePath] = '未同步';
          });
          console.log('handleFolderUpload 后立即设置本地状态:', newStatuses);
          return newStatuses;
        });
        
        // 批量更新后端状态
        const statusUpdatePromises = uploadedFilePaths.map(async (filePath) => {
          try {
            await updateFileStatusToUnsynced(filePath);
          } catch (statusError) {
            console.error('更新文件状态失败:', filePath, statusError);
          }
        });
        
        // 等待状态更新完成
        await Promise.allSettled(statusUpdatePromises);
        
        // 给状态传播一些时间
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // *** Call loadPortalData() to refresh correctly ***
        await loadPortalData();
      } else {
        // Check for specific 405 error if portal endpoint doesn't exist yet
        if(response.status === 405 && currentPortal) {
          showToast('上传失败: Portal 文件夹上传功能尚未实现', 'error');
        } else {
          showToast('上传失败: ' + (data.error || '未知错误'), 'error');
        }
      }
    } catch (err) {
      showToast('上传错误: ' + err.message, 'error');
    } finally {
      setUploading(false);
      setUploadProgress(0);
      
      // 清除文件夹选择器的选择内容
      if (folderInputRef.current) {
        folderInputRef.current.value = '';
      }
    }
  };

  // Adjust getPreviewUrl and handleDownloadFile to check for portal files
  const getFileUrl = (file, download = false) => {
    console.log("[getFileUrl] Generating URL for file:", file); 
    
    if (file.isPortalFile && file.portalId && file.deviceId && file.path) {
      console.log("[getFileUrl] Detected Portal File. Using download API.");
      
      // ** FIX: Extract path relative to device directory **
      // The file.path from admin API might be like "portalName/deviceId/actual/file/path.jpg"
      // We need to extract "actual/file/path.jpg"
      let relativePathForApi = file.path; 
      // Construct the expected prefix (portalName/deviceId)
      // We need the portalName. If not directly on file, maybe via related folder or fetch?
      // Assuming the logical path structure holds: portalName/deviceId/...
      const pathSegments = file.path.split('/');
      if (pathSegments.length > 1) { // Check if there's at least portalName/deviceId
        // Remove the first two segments (portalName, deviceId)
        relativePathForApi = pathSegments.slice(2).join('/'); 
      } else {
        // This case shouldn't happen if it's portal content, but handle defensively
        console.warn(`[getFileUrl] Portal file path seems malformed: ${file.path}`);
        // Maybe fallback to just the filename if path is just portalName/deviceId?
        relativePathForApi = path.basename(file.path); 
      }
      console.log(`[getFileUrl] Extracted relative path for API: ${relativePathForApi}`);

      // Use the extracted relative path for the API call
      let url = `/api/portal/${file.portalId}/files/download?path=${encodeURIComponent(relativePathForApi)}&deviceId=${encodeURIComponent(file.deviceId)}`;
      
      if (download) {
          url += '&download=true';
          console.log(`[getFileUrl] Generated download URL: ${url}`);
      } else {
          console.log(`[getFileUrl] Generated preview URL: ${url}`);
      }
      return url;
    } else if (!file.isPortalFile && file.path) { 
      console.log("[getFileUrl] Detected Public File. Using direct /uploads/ path.");
      let url = `/uploads/${encodeURIComponent(file.path)}`;
      if (download) {
         url += '?download=true'; 
         console.log(`[getFileUrl] Generated public download URL: ${url}`);
      } else {
         console.log(`[getFileUrl] Generated public preview URL: ${url}`);
      }
      return url;
    } else {
        console.error("[getFileUrl] Could not determine URL type for file:", file);
        return '#error-generating-url'; 
    }
  };

  const handleDownloadFile = (file) => {
    const url = getFileUrl(file, true);
    if (url && url !== '#error-generating-url') {
        window.open(url, '_blank');
    } else {
        showToast('无法生成下载链接', 'error');
    }
  };

  const getPreviewUrl = (file) => {
      const url = getFileUrl(file, false);
      // Handle the error case maybe differently for preview if needed
      return url === '#error-generating-url' ? null : url; 
  };

  // 预览文件
  const handlePreviewFile = (file) => {
    setPreviewFile(file);
    setShowPreviewModal(true);
  };

  // 关闭预览
  const closePreview = () => {
    setShowPreviewModal(false);
    setPreviewFile(null);
  };

  // 判断文件是否可以在浏览器中预览
  const canPreviewInBrowser = (file) => {
    const previewableTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf', 'txt', 'mp4', 'mp3', 'wav'];
    return previewableTypes.includes(file.type.toLowerCase());
  };

  // 更改文件状态
  const handleChangeStatus = async (file, newStatus) => {
    try {
      // 显示正在切换状态的提示
      const fileName = file.name || file.filename || file.path.split('/').pop();
      showToast(`正在切换 ${fileName} 的状态...`, 'info');
      
      // 根据新状态调用对应的函数
      if (newStatus === 'uploaded' || newStatus === '已同步') {
        await markFileAsSynced(file.path);
      } else if (newStatus === 'pending' || newStatus === '未同步') {
        await updateFileStatusToUnsynced(file.path);
      }
      
      // 更新本地文件列表中的状态显示
      const displayStatus = newStatus === 'uploaded' || newStatus === '已同步' ? '已同步' : '未同步';
      setSyncStatuses(prevStatuses => ({
        ...prevStatuses,
        [file.path]: displayStatus,
      }));
      
      // showToast在各自的函数中已经有了，这里不需要重复
    } catch (err) {
      console.error('更改状态失败:', err);
      showToast('更改状态失败: ' + err.message, 'error');
    }
  };

  const handleDeleteItem = async (item) => {
    const itemName = item.filename || item.name;
    if (!window.confirm(`确定要删除 ${item.isFolder ? '文件夹' : '文件'} "${itemName}" 吗？`)) {
      return;
    }
    
    try {
      let response;
      let url;
      
      // 判断是否在 Portal 内部
      if (currentPortal) {
        // --- Portal 内部删除逻辑 ---
        // 构造 Portal 删除 API 的 URL，使用处理好的相对路径 item.path
        url = `/api/portal/${currentPortal.id}/files?path=${encodeURIComponent(item.path)}&isFolder=${!!item.isFolder}`;
        response = await fetch(url, {
          method: 'DELETE',
        });
        // --- End Portal 删除逻辑 ---
      } else {
        // --- 非 Portal (根目录) 删除逻辑 (保持不变) ---
        url = `/api/admin/files?path=${encodeURIComponent(item.path)}&isFolder=${!!item.isFolder}`;
        response = await fetch(url, {
          method: 'DELETE',
        });
        // --- End 非 Portal 删除逻辑 ---
      }
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        showToast(`成功删除 ${itemName}!`);
        
        // 立即从本地状态中移除被删除的项目
        setSyncStatuses(prevStatuses => {
          const newStatuses = { ...prevStatuses };
          
          // 删除主项目的状态
          delete newStatuses[item.path];
          
          // 如果是文件夹，删除其中所有子项目的状态
          if (item.isFolder) {
            const folderPrefix = item.path + '/';
            Object.keys(newStatuses).forEach(statusPath => {
              if (statusPath.startsWith(folderPrefix)) {
                delete newStatuses[statusPath];
              }
            });
          }
          
          console.log(`已清理删除项目的本地状态: ${item.path}`, newStatuses);
          return newStatuses;
        });
        
        // === 增强的状态清理：清理localStorage中的上传时间戳记录 ===
        try {
          const timestamps = JSON.parse(localStorage.getItem('uploadTimestamps') || '{}');
          let removedCount = 0;
          
          // 删除主项目的时间戳
          if (timestamps[item.path]) {
            delete timestamps[item.path];
            removedCount++;
          }
          
          // 如果是文件夹，删除所有子项目的时间戳
          if (item.isFolder) {
            const folderPrefix = item.path + '/';
            Object.keys(timestamps).forEach(timestampPath => {
              if (timestampPath.startsWith(folderPrefix)) {
                delete timestamps[timestampPath];
                removedCount++;
              }
            });
          }
          
          if (removedCount > 0) {
            localStorage.setItem('uploadTimestamps', JSON.stringify(timestamps));
            console.log(`[状态清理] 已清理 ${removedCount} 个上传时间戳记录: ${item.path}`);
          }
        } catch (error) {
          console.warn('[状态清理] 清理上传时间戳失败:', error);
        }
        
        // === 彻底清理localStorage中的其他状态缓存 ===
        try {
          let needsUpdate = false;
          
          // 清理fileStatuses
          try {
            const fileStatuses = JSON.parse(localStorage.getItem('fileStatuses') || '{}');
            let removed = 0;
            
            Object.keys(fileStatuses).forEach(statusPath => {
              if (statusPath === item.path || 
                  (item.isFolder && statusPath.startsWith(item.path + '/'))) {
                delete fileStatuses[statusPath];
                removed++;
              }
            });
            
            if (removed > 0) {
              localStorage.setItem('fileStatuses', JSON.stringify(fileStatuses));
              console.log(`[状态清理] 从fileStatuses清理了 ${removed} 个记录`);
              needsUpdate = true;
            }
          } catch (error) {
            console.warn('[状态清理] 清理fileStatuses失败:', error);
          }
          
          // 清理syncStatuses  
          try {
            const syncStatuses = JSON.parse(localStorage.getItem('syncStatuses') || '{}');
            let removed = 0;
            
            Object.keys(syncStatuses).forEach(statusPath => {
              if (statusPath === item.path || 
                  (item.isFolder && statusPath.startsWith(item.path + '/'))) {
                delete syncStatuses[statusPath];
                removed++;
              }
            });
            
            if (removed > 0) {
              localStorage.setItem('syncStatuses', JSON.stringify(syncStatuses));
              console.log(`[状态清理] 从syncStatuses清理了 ${removed} 个记录`);
              needsUpdate = true;
            }
          } catch (error) {
            console.warn('[状态清理] 清理syncStatuses失败:', error);
          }
          
          // 清理recentUploads
          try {
            const recentUploads = JSON.parse(localStorage.getItem('recentUploads') || '[]');
            const filteredUploads = recentUploads.filter(uploadPath => {
              return uploadPath !== item.path && 
                     !(item.isFolder && uploadPath.startsWith(item.path + '/'));
            });
            
            if (filteredUploads.length !== recentUploads.length) {
              localStorage.setItem('recentUploads', JSON.stringify(filteredUploads));
              console.log(`[状态清理] 从recentUploads清理了 ${recentUploads.length - filteredUploads.length} 个记录`);
              needsUpdate = true;
            }
          } catch (error) {
            console.warn('[状态清理] 清理recentUploads失败:', error);
          }
          
          if (needsUpdate) {
            console.log(`[状态清理] localStorage缓存清理完成`);
          }
        } catch (error) {
          console.error('[状态清理] localStorage清理过程失败:', error);
        }
        
        // 通知WebSocket服务器清理状态记录
        if (wsConnected) {
          sendWebSocketMessage({
            type: 'remove_file_status',
            filename: item.path,
            isFolder: !!item.isFolder,
            timestamp: Date.now()
          });
        }
        
        // 重新加载当前目录的数据
        await loadPortalData(); 
      } else {
        throw new Error(data.error || '删除失败');
      }
    } catch (err) {
      console.error('删除失败:', err);
      showToast(`删除 ${itemName} 失败: ${err.message}`, 'error');
    }
  };

  const handleCreateFolder = async (e) => {
    e.preventDefault();
    
    if (!newFolderName.trim()) {
      showToast('请输入文件夹名称', 'error');
      return;
    }
    
    setCreatingFolder(true);
    
    try {
      let response;
      let url;
      let method;
      let body;

      // 判断是否在 Portal 内部
      if (currentPortal) {
        // --- Portal 内部创建文件夹逻辑 ---
        url = `/api/portal/${currentPortal.id}/files`;
        method = 'POST'; // Use POST for creation within portal API
        body = JSON.stringify({
          folderName: newFolderName.trim(),
          currentPath: currentPath, // Should be the relative path within portal
        });
        response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: body,
        });
        // --- End Portal 创建文件夹逻辑 ---
      } else {
        // --- 非 Portal (根目录) 创建文件夹逻辑 (保持不变) ---
        url = '/api/admin/files';
        method = 'PUT'; // Keep original method for admin API
        body = JSON.stringify({
          folderName: newFolderName.trim(),
          currentPath: currentPath,
        });
        response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: body,
        });
        // --- End 非 Portal 创建文件夹逻辑 ---
      }
      
      const data = await response.json();
      
      if (response.ok && data.success) { // Assuming backend returns { success: true }
        setShowNewFolderModal(false);
        setNewFolderName('');
        // 重新加载当前目录的数据
        await loadPortalData(); 
        showToast('文件夹创建成功');
      } else {
        throw new Error(data.error || '创建文件夹失败');
      }
    } catch (err) {
      console.error('创建文件夹失败:', err);
      showToast(`创建文件夹失败: ${err.message}`, 'error');
    } finally {
      setCreatingFolder(false);
    }
  };

  const navigateToFolder = async (folder) => {
    console.log("[navigateToFolder] Navigating to:", folder);
    // Use folder.path which should be the correct relative path from admin API
    const newPath = folder.path;
    await Promise.all([
      new Promise(resolve => {
        setCurrentPath(newPath);
        resolve();
      }),
      new Promise(resolve => {
        // Reconstruct history based on the new path
        const pathParts = newPath.split('/').filter(Boolean);
        const newHistory = [{ name: '根目录', path: '/' }];
        let currentBuiltPath = '';
        pathParts.forEach(part => {
          currentBuiltPath = currentBuiltPath ? `${currentBuiltPath}/${part}` : part;
          newHistory.push({ name: part, path: currentBuiltPath }); 
        });
        setPathHistory(newHistory);
        resolve();
      })
    ]);
    // loadPortalData will be triggered by useEffect watching currentPath
  }

  const navigateToPath = (pathIndex) => {
    const newHistory = pathHistory.slice(0, pathIndex + 1);
    setPathHistory(newHistory);
    setCurrentPath(newHistory[newHistory.length - 1].path);
    // loadPortalData will be triggered by useEffect watching currentPath
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(k)));
    return Math.round(bytes / Math.pow(k, i), 2) + ' ' + sizes[i];
  };

  // 获取文件状态显示内容
  const getStatusDisplay = (file) => {
    if (file.isFolder) return { text: '--', className: '' }
    
    const statusMap = {
      'pending': { 
        text: '待上传', 
        className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
      },
      'uploaded': {
        text: '已上传',
        className: 'bg-green-100 text-green-800 hover:bg-green-200'
      },
      'processing': {
        text: '处理中',
        className: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
      },
      'error': {
        text: '错误',
        className: 'bg-red-100 text-red-800 hover:bg-red-200'
      }
    }
    
    return statusMap[file.status] || {
      text: file.status || '未知',
      className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
    }
  };

  // 处理拖放的文件夹
  const handleDroppedFolder = async (items) => {
    showToast('开始处理文件夹上传...', 'info');
    
    try {
      // 收集所有文件和它们的相对路径
      const files = [];
      let hasFiles = false;
      const topLevelFolderName = [];
      
      // 限制并发操作和打开的文件数量
      const MAX_CONCURRENT = 5; // 最大并发数
      const pendingEntries = [];
      let activeCount = 0;

      // 遍历所有被拖拽的项目并收集文件夹
      const collectFiles = async (items) => {
        // 首先收集顶层项目
        for (let i = 0; i < items.length; i++) {
          const entry = items[i].webkitGetAsEntry();
          if (entry) {
            if (entry.isDirectory) {
              topLevelFolderName.push(entry.name);
              pendingEntries.push({ entry, path: entry.name });
            } else {
              // 如果是顶层文件，直接添加
              const file = items[i].getAsFile();
              if (file) {
                files.push({ file, path: file.name });
                hasFiles = true;
              }
            }
          }
        }
        
        // 使用信号量控制并发处理
        const processNext = async () => {
          if (pendingEntries.length === 0) {
            return;
          }
          
          if (activeCount >= MAX_CONCURRENT) {
            // 如果达到最大并发数，等待
            return;
          }
          
          activeCount++;
          const entryInfo = pendingEntries.shift();
          
          try {
            await processEntry(entryInfo);
          } catch (err) {
            console.error('处理条目错误:', err);
          } finally {
            activeCount--;
            // 处理下一个
            await processNext();
          }
          
          // 继续处理其他等待的条目
          if (pendingEntries.length > 0) {
            await processNext();
          }
        };

        // 递归处理文件夹
        const processEntry = async (entryInfo) => {
          const { entry, path } = entryInfo;
          
          if (!entry || !entry.isDirectory) {
            return;
          }
          
          try {
            // 逐批次读取目录内容
            const entries = await readDirectoryEntries(entry);
            
            // 处理所有条目
            for (const childEntry of entries) {
              const childPath = path + '/' + childEntry.name;
              
              if (childEntry.isDirectory) {
                // 将子目录添加到待处理队列
                pendingEntries.push({ entry: childEntry, path: childPath });
                // 如果活动数量低于最大值，启动下一个处理
                if (activeCount < MAX_CONCURRENT) {
                  await processNext();
                }
              } else {
                // 处理文件
                try {
                  const file = await getFileFromEntry(childEntry);
                  if (file) {
                    files.push({ file, path: childPath });
                    hasFiles = true;
                  }
                } catch (err) {
                  console.error('获取文件错误:', err);
                }
              }
            }
          } catch (err) {
            console.error('处理目录错误:', err);
          }
        };
        
        // 读取目录内容，避免一次性打开太多文件
        const readDirectoryEntries = async (dirEntry) => {
          return new Promise((resolve, reject) => {
            const allEntries = [];
            const reader = dirEntry.createReader();
            
            // 分批次读取目录内容
            const readEntries = () => {
              reader.readEntries(entries => {
                if (entries.length === 0) {
                  resolve(allEntries);
                } else {
                  allEntries.push(...entries);
                  // 给系统一些时间关闭文件句柄
                  setTimeout(readEntries, 50);
                }
              }, reject);
            };
            
            readEntries();
          });
        };
        
        // 从文件条目获取文件对象
        const getFileFromEntry = (fileEntry) => {
          return new Promise((resolve, reject) => {
            fileEntry.file(resolve, reject);
          });
        };
        
        // 开始并发处理
        const startProcessors = [];
        for (let i = 0; i < Math.min(MAX_CONCURRENT, pendingEntries.length); i++) {
          startProcessors.push(processNext());
        }
        
        // 等待所有处理器完成
        await Promise.all(startProcessors);
        
        // 确保所有条目都被处理
        while (pendingEntries.length > 0 || activeCount > 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      };
      
      // 开始收集文件
      await collectFiles(items);
      
      if (!hasFiles || files.length === 0) {
        showToast('没有找到可上传的文件', 'error');
        return;
      }
      
      // 上传收集到的文件 (分批上传以避免一次处理太多文件)
      await uploadFolderStructureInBatches(files, topLevelFolderName[0] || '文件夹');
    } catch (err) {
      console.error('处理拖拽文件夹错误:', err);
      showToast('处理文件夹失败: ' + err.message, 'error');
    }
  };
  
  // 分批上传文件夹结构
  const uploadFolderStructureInBatches = async (files, folderName) => {
    if (files.length === 0) return;
    
    // 将文件分批处理，每批最多处理50个文件
    const BATCH_SIZE = 50;
    const totalFiles = files.length;
    const batchCount = Math.ceil(totalFiles / BATCH_SIZE);
    
    showToast(`准备上传文件夹"${folderName}"，共 ${totalFiles} 个文件，分 ${batchCount} 批处理...`, 'info');
    
    try {
      setUploading(true);
      
      for (let batchIndex = 0; batchIndex < batchCount; batchIndex++) {
        const start = batchIndex * BATCH_SIZE;
        const end = Math.min(start + BATCH_SIZE, totalFiles);
        const batchFiles = files.slice(start, end);
        
        // 更新进度
        const progress = Math.round((batchIndex / batchCount) * 100);
        setUploadProgress(progress);
        
        showToast(`上传第 ${batchIndex + 1}/${batchCount} 批，共 ${batchFiles.length} 个文件...`, 'info');
        
        const formData = new FormData();
        formData.append('currentPath', currentPath);
        
        // 添加这一批的文件和路径
        for (let i = 0; i < batchFiles.length; i++) {
          const { file, path } = batchFiles[i];
          formData.append('file', file);
          formData.append('filePaths', path);
        }
        
        // 上传这一批
        const response = await fetch('/api/admin/files/folder', {
          method: 'POST',
          body: formData,
        });
        
        if (!response.ok) {
          const data = await response.json();
          throw new Error('上传失败: ' + (data.error || '未知错误'));
        }
        
        // 清理内存
        formData.delete('file');
        formData.delete('filePaths');
      }
      
      // 所有批次完成
      setUploadProgress(100);
      showToast(`成功上传文件夹 "${folderName}"，共 ${totalFiles} 个文件`);
      fetchFiles();
    } catch (err) {
      showToast('上传错误: ' + err.message, 'error');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const loadPortalData = async () => {
    console.log(`[loadPortalData] Loading Admin view for path: '${currentPath}'`);
    try {
      setLoading(true);
      setError(null);
      setFiles([]); // Clear previous results
      setFolders([]);
      setPortals([]);
      setCurrentPortal(null); // Reset portal state for admin view
      // 注意：不要清空 syncStatuses，保留用户手动设置的状态

      // --- Always use Admin API --- 
      let fetchedFiles = [];
      let fetchedFolders = [];
      let fetchedPortalsData = []; // To store raw portal data if needed
      
      // 保存当前的状态，避免被覆盖
      const currentSyncStatuses = { ...syncStatuses };

      try {
        // Fetch files and folders from the admin endpoint for the current path
        const apiPath = currentPath === '/' ? '/' : currentPath;
        const apiUrl = `/api/admin/files?path=${encodeURIComponent(apiPath)}`;
        console.log(`[loadPortalData] Fetching from Admin API: ${apiUrl}`);
        const filesResponse = await fetch(apiUrl);
        const filesData = await filesResponse.json();
        console.log(`[loadPortalData] Admin API response status: ${filesResponse.status}`);
        console.log("[loadPortalData] Raw Admin API response data:", filesData);
        
        if (!filesResponse.ok) {
          throw new Error(filesData.error || `获取文件列表失败 (${filesResponse.status})`);
        }

        // Process files returned by the admin API
        fetchedFiles = (filesData.files || []).map(apiFile => ({
          // Spread all properties returned by the API first
          ...apiFile, 
          // Then ensure critical frontend properties are set correctly
          id: apiFile.path || apiFile.id, // Use path as ID if available
          filename: apiFile.name || path.basename(apiFile.path || ''),
          type: apiFile.type || path.extname(apiFile.path || '').slice(1) || 'unknown',
          // Ensure these flags are explicitly carried over
          isPortalFile: apiFile.isPortalFile || false, 
          portalId: apiFile.portalId || null,
          deviceId: apiFile.deviceId || null,
          // path should be the logical path used for navigation/display
          path: apiFile.path 
        }));
        
        // Process folders returned by the admin API
        fetchedFolders = (filesData.folders || []).map(apiFolder => ({
          // Spread all properties returned by the API first
          ...apiFolder,
          // Then ensure critical frontend properties are set correctly
          id: apiFolder.path || apiFolder.id, // Use path as ID if available
          filename: apiFolder.name,
          isFolder: true,
          type: apiFolder.type || 'folder', // Use type from API if provided (e.g., 'portal', 'device')
          // Ensure these flags are explicitly carried over
          isPortal: apiFolder.isPortal || false, 
          portal: apiFolder.portal || null, 
          isDeviceFolder: apiFolder.isDeviceFolder || false, 
          portalId: apiFolder.portalId || null,      
          deviceId: apiFolder.deviceId || null,
          // path should be the logical path used for navigation/display
          path: apiFolder.path 
        }));

        // --- Portal Handling at Root (Merging) --- 
        if (currentPath === '/') {
            // If portals weren't included in the main API response, fetch them separately (fallback)
            const hasPortalsInResponse = fetchedFolders.some(f => f.isPortal);
            if (!hasPortalsInResponse) {
                console.log('[loadPortalData] Admin API did not return portals, fetching separately...');
                 try {
                  const portalsResponse = await fetch('/api/admin/upload-portals');
                  if (portalsResponse.ok) {
                    const portalsData = await portalsResponse.json();
                    fetchedPortalsData = (portalsData.portals || []); 
                    const portalFoldersToAdd = fetchedPortalsData.map(p => ({
                      // Ensure this structure matches the main folder mapping
                      ...p, // Include all portal data
                      name: p.name,
                      filename: p.name,
                      isFolder: true,
                      isPortal: true, 
                      portal: p,      
                      type: 'portal', 
                      path: p.name, // Logical path at root
                      // Use stats from portal data
                      size: p.stats?.totalSize || 0, 
                      fileCount: p.stats?.uploads || 0,
                      id: p.id,
                      portalId: p.id,
                      deviceId: null,
                      modifiedAt: p.updatedAt // Add modified time if available
                    }));
                    // Prepend portal folders
                    fetchedFolders = [...portalFoldersToAdd, ...fetchedFolders]; 
                  } else {
                     console.error('Failed to fetch portals separately.');
                  }
                } catch (portalErr) {
                     console.error('Error fetching portals separately:', portalErr);
                }
            }
        }
        // --- End Portal Handling ---
        
        // 获取实际的同步状态，优先保留用户手动设置的状态
        const updatedSyncStatuses = await loadActualSyncStatuses(fetchedFiles, fetchedFolders, currentSyncStatuses);

        // Update state
        console.log("[loadPortalData] Final Processed Files:", fetchedFiles);
        console.log("[loadPortalData] Final Processed Folders:", fetchedFolders);
        setFiles(fetchedFiles);
        setFolders(fetchedFolders); 
        setPortals(fetchedPortalsData); // Store raw portal data if needed
        setSyncStatuses(updatedSyncStatuses);

      } catch (err) {
        console.error('Error loading admin files/portals:', err);
        const errorMessage = err instanceof Error ? err.message : String(err);
        setError(errorMessage);
        // Clear state on error
        setFiles([]); setFolders([]); setPortals([]); 
        // 错误时也不要清空状态，保留用户的手动设置
      }
    } catch (err) { 
       // General error handling for the outer try block (e.g., setLoading)
       console.error('[loadPortalData] General Error:', err);
       const errorMessage = err instanceof Error ? err.message : String(err);
       setError(`加载数据失败: ${errorMessage}`);
       setFiles([]); setFolders([]); 
       // 错误时也不要清空状态，保留用户的手动设置
     } finally {
       setLoading(false);
     }
  };

  // 新增函数：从后端读取实际的同步状态，同时保留用户手动设置的状态
  const loadActualSyncStatuses = async (fetchedFiles, fetchedFolders, currentStatuses) => {
    const updatedStatuses = { ...currentStatuses };
    
    try {
      // 从WebSocket服务器获取状态数据
      const syncStatusResponse = await fetch('/api/admin/sync-statuses');
      let backendStatuses = {};
      
      if (syncStatusResponse.ok) {
        const statusData = await syncStatusResponse.json();
        backendStatuses = statusData.statuses || {};
        console.log('[loadActualSyncStatuses] 从后端获取的状态:', backendStatuses);
      } else {
        console.warn('[loadActualSyncStatuses] 无法获取后端状态数据，使用默认值');
      }
      
      // === 获取最近上传文件列表（防止状态被错误覆盖） ===
      const recentUploadTime = Date.now() - (5 * 60 * 1000); // 5分钟内的上传
      const recentUploads = new Set();
      
      // 从localStorage中获取最近上传的文件
      try {
        const uploadTimestamps = JSON.parse(localStorage.getItem('uploadTimestamps') || '{}');
        const recentUploads_array = JSON.parse(localStorage.getItem('recentUploads') || '[]');
        
        Object.keys(uploadTimestamps).forEach(filePath => {
          if (uploadTimestamps[filePath] > recentUploadTime) {
            recentUploads.add(filePath);
            console.log(`[状态保护] 识别最近上传文件: ${filePath}`);
          }
        });
        
        // 同时检查recentUploads数组中的文件
        recentUploads_array.forEach(filePath => {
          recentUploads.add(filePath);
        });
      } catch (error) {
        console.warn('[loadActualSyncStatuses] 无法获取最近上传记录:', error);
      }
      
      // 对于文件/文件夹，应用智能状态合并策略
      [...fetchedFiles, ...fetchedFolders].forEach(item => {
        const itemPath = item.path;
        const currentStatus = currentStatuses[itemPath];
        const backendStatus = backendStatuses[itemPath];
        
        // === 策略1: 最近上传的文件保持"未同步"状态 ===
        if (recentUploads.has(itemPath)) {
          if (currentStatus === '未同步') {
            updatedStatuses[itemPath] = '未同步';
            console.log(`[状态保护] 保持最近上传文件的未同步状态: ${itemPath}`);
            return;
          }
        }
        
        // === 策略2: 用户手动设置的状态优先 ===
        if (currentStatus && currentStatus !== '未知' && currentStatus !== 'undefined') {
          // 用户已设置状态，检查是否需要保持
          if (currentStatus === '未同步' && backendStatus === '已同步') {
            // 前端显示未同步，后端显示已同步 - 可能是状态不一致
            console.warn(`[状态冲突] ${itemPath}: 前端=${currentStatus}, 后端=${backendStatus}`);
            
            // 对于最近上传的文件，优先保持前端的"未同步"状态
            if (recentUploads.has(itemPath) || 
                (currentStatus === '未同步' && item.createdAt && 
                 new Date(item.createdAt).getTime() > recentUploadTime)) {
              updatedStatuses[itemPath] = '未同步';
              console.log(`[状态保护] 保持新文件的未同步状态: ${itemPath}`);
            } else {
              // 对于较老的文件，使用后端状态
              updatedStatuses[itemPath] = backendStatus || '未同步';
              console.log(`[状态同步] 使用后端状态: ${itemPath} -> ${backendStatus}`);
            }
          } else {
            // 其他情况保持用户设置
            updatedStatuses[itemPath] = currentStatus;
          }
        } else {
          // === 策略3: 新发现的文件使用后端状态或默认值 ===
          updatedStatuses[itemPath] = backendStatus || '未同步';
          console.log(`[新文件] 设置状态: ${itemPath} -> ${updatedStatuses[itemPath]}`);
        }
      });
      
      console.log('[loadActualSyncStatuses] 合并后的状态:', updatedStatuses);
      console.log('[loadActualSyncStatuses] 最近上传文件数量:', recentUploads.size);
      
      return updatedStatuses;
    } catch (error) {
      console.error('[loadActualSyncStatuses] 获取状态失败:', error);
      
      // 如果无法获取后端状态，对于新文件使用默认值，保持现有状态
      [...fetchedFiles, ...fetchedFolders].forEach(item => {
        if (!(item.path in currentStatuses) || !currentStatuses[item.path]) {
          updatedStatuses[item.path] = '未同步';
        }
      });
      
      return updatedStatuses;
    }
  };

  // 获取文件图标或缩略图
  const getFileIcon = (file) => {
    // 如果是图片文件且有缩略图URL，返回缩略图
    if (file.thumbnailUrl) {
      return file.thumbnailUrl
    }
    
    // 如果是图片类型，返回原始文件作为缩略图
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp']
    if (imageTypes.includes(file.type.toLowerCase())) {
      return getPreviewUrl(file) // 使用 getPreviewUrl 来获取正确的文件路径
    }
    
    // 如果是文件夹，返回对应的图标
    if (file.isFolder) {
      if (file.isParent) {
        return '/static/file-types/folder-up.svg'
      }
      if (file.isDynamicPortal) {
        return '/static/file-types/dynamic-portal.svg'
      }
      return '/static/file-types/folder.svg'
    }
    
    const iconMap = {
      // 图片文件
      'jpg': 'image',
      'jpeg': 'image',
      'png': 'image',
      'gif': 'image',
      'webp': 'image',
      'svg': 'image',
      // 文档文件
      'pdf': 'pdf',
      'doc': 'word',
      'docx': 'word',
      'xls': 'excel',
      'xlsx': 'excel',
      'ppt': 'powerpoint',
      'pptx': 'powerpoint',
      'txt': 'text',
      // 压缩文件
      'zip': 'compressed',
      'rar': 'compressed',
      '7z': 'compressed',
      'tar': 'compressed',
      'gz': 'compressed',
      // 音视频文件
      'mp3': 'audio',
      'wav': 'audio',
      'mp4': 'video',
      'mov': 'video',
      'avi': 'video',
      // 代码文件
      'js': 'code',
      'jsx': 'code',
      'ts': 'code',
      'tsx': 'code',
      'html': 'code',
      'css': 'code',
      'json': 'code',
    }
    
    const ext = file.type.toLowerCase()
    const iconType = iconMap[ext] || 'default'
    return `/static/file-types/${iconType}.svg`
  }

  // 格式化上传时间
  const formatUploadTime = (timestamp) => {
    if (!timestamp) return '未知时间'
    const date = new Date(timestamp)
    if (isNaN(date.getTime())) return '无效时间'
    
    const now = new Date()
    const diff = now - date
    
    // 如果是今天
    if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
      return date.toLocaleTimeString('zh-CN', { hour12: false })
    }
    
    // 如果是昨天
    if (diff < 48 * 60 * 60 * 1000 && date.getDate() === now.getDate() - 1) {
      return `昨天 ${date.toLocaleTimeString('zh-CN', { hour12: false })}`
    }
    
    // 如果是今年
    if (date.getFullYear() === now.getFullYear()) {
      return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }) + 
             ' ' + date.toLocaleTimeString('zh-CN', { hour12: false })
    }
    
    // 其他情况显示完整日期
    return date.toLocaleDateString('zh-CN') + ' ' + 
           date.toLocaleTimeString('zh-CN', { hour12: false })
  }

  // 添加返回上一级函数
  const navigateUp = () => {
    if (currentPath === '/') return; // Already at root
    const parentPath = path.dirname(currentPath);
    const newPath = parentPath === '.' || parentPath === '' ? '/' : parentPath;
    const newHistory = pathHistory.slice(0, -1);
    setPathHistory(newHistory);
    setCurrentPath(newPath);
    // loadPortalData will be triggered by useEffect watching currentPath
  }

  // 获取状态标签的样式
  const getStatusStyle = (status) => {
    const baseStyle = "inline-block px-2 py-1 rounded-full text-xs font-medium cursor-pointer transition-colors hover:opacity-80";
    
    switch (status) {
      case '已同步':
        return `${baseStyle} bg-green-100 text-green-800 hover:bg-green-200`;
      case '同步中':
        return `${baseStyle} bg-blue-100 text-blue-800 hover:bg-blue-200`;
      case '同步失败':
        return `${baseStyle} bg-red-100 text-red-800 hover:bg-red-200`;
      case '未同步':
      default:
        return `${baseStyle} bg-yellow-100 text-yellow-800 hover:bg-yellow-200`;
    }
  };

  // === 状态一致性检查函数 ===
  const performStatusConsistencyCheck = async () => {
    try {
      console.log('[状态一致性检查] 开始检查前端和后端状态同步...');
      
      // 获取当前前端状态
      const currentFrontendStatuses = syncStatuses;
      
      // 获取后端状态
      const syncStatusResponse = await fetch('/api/admin/sync-statuses');
      if (!syncStatusResponse.ok) {
        console.warn('[状态一致性检查] 无法获取后端状态，跳过检查');
        return;
      }
      
      const statusData = await syncStatusResponse.json();
      const backendStatuses = statusData.statuses || {};
      
      const conflicts = [];
      const frontendOnly = [];
      const backendOnly = [];
      
      // 检查前端状态
      Object.keys(currentFrontendStatuses).forEach(filePath => {
        const frontendStatus = currentFrontendStatuses[filePath];
        const backendStatus = backendStatuses[filePath];
        
        if (!backendStatus) {
          frontendOnly.push({ filePath, status: frontendStatus });
        } else if (frontendStatus !== backendStatus) {
          conflicts.push({
            filePath,
            frontend: frontendStatus,
            backend: backendStatus
          });
        }
      });
      
      // 检查后端独有状态
      Object.keys(backendStatuses).forEach(filePath => {
        if (!currentFrontendStatuses[filePath]) {
          backendOnly.push({ filePath, status: backendStatuses[filePath] });
        }
      });
      
      // 输出检查结果
      console.log('[状态一致性检查] 检查完成:', {
        totalFrontend: Object.keys(currentFrontendStatuses).length,
        totalBackend: Object.keys(backendStatuses).length,
        conflicts: conflicts.length,
        frontendOnly: frontendOnly.length,
        backendOnly: backendOnly.length
      });
      
      if (conflicts.length > 0) {
        console.warn('[状态一致性检查] 发现状态冲突:', conflicts);
        
        // 对于冲突的文件，检查是否是最近上传的
        const recentUploadTime = Date.now() - (5 * 60 * 1000);
        conflicts.forEach(conflict => {
          // 获取文件创建时间或上传时间戳
          const file = files.find(f => f.path === conflict.filePath);
          const isRecentFile = file && file.createdAt && 
                              new Date(file.createdAt).getTime() > recentUploadTime;
          
          if (isRecentFile || conflict.frontend === '未同步') {
            console.log(`[状态一致性检查] 保持前端状态 (最近文件): ${conflict.filePath} = ${conflict.frontend}`);
          } else {
            console.log(`[状态一致性检查] 建议同步到后端状态: ${conflict.filePath} -> ${conflict.backend}`);
          }
        });
      }
      
      if (frontendOnly.length > 0) {
        console.info('[状态一致性检查] 前端独有状态 (可能需要清理):', frontendOnly);
      }
      
      if (backendOnly.length > 0) {
        console.info('[状态一致性检查] 后端独有状态 (可能需要同步):', backendOnly);
      }
      
      return {
        conflicts,
        frontendOnly,
        backendOnly,
        healthy: conflicts.length === 0 && frontendOnly.length === 0
      };
      
    } catch (error) {
      console.error('[状态一致性检查] 检查失败:', error);
      return null;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {toast && (
        <div 
          className={`fixed top-6 left-1/2 transform -translate-x-1/2 z-50 px-6 py-3 rounded-md shadow-lg ${
            toast.type === 'success' 
              ? 'bg-green-100 text-green-800 border-l-4 border-green-500' 
              : toast.type === 'error' 
              ? 'bg-red-100 text-red-800 border-l-4 border-red-500' 
              : 'bg-blue-100 text-blue-800 border-l-4 border-blue-500'
          } flex items-center transition-all duration-300 ${
            toastVisible ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'
          }`}
        >
          {toast.type === 'success' && (
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          )}
          {toast.type === 'error' && (
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          )}
          <span className="font-medium">{toast.message}</span>
        </div>
      )}

      <header className="mb-8">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <h1 className="text-3xl font-bold">文件管理</h1>
            {/* WebSocket连接状态指示器 */}
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
              wsConnected 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                wsConnected ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span>{wsConnected ? '已连接服务器' : '服务器断开'}</span>
            </div>
            {/* 刷新按钮 */}
            <button 
              onClick={() => {
                loadPortalData();
                showToast('正在刷新文件状态...', 'info');
              }}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm"
              title="刷新文件状态"
            >
              刷新状态
            </button>
          </div>
          <Link href="/" className="btn-secondary">
            返回首页
          </Link>
        </div>
      </header>

      {/* 面包屑导航 */}
      <div className="flex items-center mb-6 overflow-x-auto whitespace-nowrap py-2">
        <div className="text-gray-600 mr-2">当前位置:</div>
        {pathHistory.map((item, index) => (
          <div key={index} className="flex items-center">
            {index > 0 && <span className="mx-2 text-gray-400">/</span>}
            <button
              onClick={() => navigateToPath(index)}
              className={`px-2 py-1 rounded hover:bg-gray-100 ${
                index === pathHistory.length - 1 ? 'font-semibold text-blue-600' : 'text-gray-600'
              }`}
            >
              {item.name}
            </button>
          </div>
        ))}
      </div>

      <div 
        ref={dropZoneRef}
        className={`bg-white shadow-md rounded-lg p-6 mb-8 relative transition-all ${
          isDragging 
            ? 'ring-2 ring-blue-500 bg-blue-50' 
            : ''
        }`}
      >
        {isDragging && (
          <div className="absolute inset-0 bg-blue-100 bg-opacity-70 flex items-center justify-center rounded-lg z-10">
            <div className="text-center p-8 rounded-lg">
              <svg className="mx-auto h-12 w-12 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              <h3 className="mt-2 text-lg font-medium text-blue-900">释放鼠标上传文件</h3>
              <p className="mt-1 text-sm text-blue-700">支持直接拖放文件或文件夹</p>
            </div>
          </div>
        )}
      
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          上传文件
          <span className="ml-2 text-sm font-normal text-gray-500">
            (支持拖拽上传文件/文件夹和粘贴上传)
          </span>
        </h2>
        
        <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              上传文件
            </label>
            <input
              type="file"
              id="fileUpload"
              ref={fileInputRef}
              onChange={(e) => handleFileUpload(e.target.files)}
              className="block w-full text-gray-500 
                        file:mr-4 file:py-2 file:px-4
                        file:rounded file:border-0
                        file:text-sm file:font-semibold
                        file:bg-blue-50 file:text-blue-700
                        hover:file:bg-blue-100"
              disabled={uploading}
              multiple
            />
            <p className="mt-1 text-sm text-gray-500">可选择多个文件进行上传</p>
          </div>
          
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              上传文件夹
            </label>
            <input
              type="file"
              id="folderUpload"
              ref={folderInputRef}
              onChange={handleFolderUpload}
              className="block w-full text-gray-500 
                        file:mr-4 file:py-2 file:px-4
                        file:rounded file:border-0
                        file:text-sm file:font-semibold
                        file:bg-green-50 file:text-green-700
                        hover:file:bg-green-100"
              webkitdirectory="true"
              directory="true"
              disabled={uploading}
            />
            <p className="mt-1 text-sm text-gray-500">选择文件夹进行上传 (保持目录结构)</p>
          </div>
        </div>
        
        <div className="mt-4 p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50">
          <div className="text-center">
            <svg className="mx-auto h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="mt-1 text-sm text-gray-500">
              将文件或<strong>文件夹</strong>拖到此处，或<strong>粘贴</strong>截图和文件
            </p>
            <p className="text-xs text-gray-400 mt-1">支持单个或多个文件，完整保留文件夹结构</p>
          </div>
        </div>
        
        {uploading && (
          <div className="mt-4">
            <div className="flex items-center">
              <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                <div 
                  className="bg-blue-600 h-2.5 rounded-full" 
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-600">{uploadProgress}%</span>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="p-6 border-b flex justify-between items-center">
          <h2 className="text-xl font-semibold">文件列表</h2>
          <button 
            onClick={() => setShowNewFolderModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            新建文件夹
          </button>
        </div>
        
        {loading ? (
          <div className="text-center p-12">
            <svg className="animate-spin h-10 w-10 text-blue-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        ) : error ? (
          <div className="text-center p-12 text-red-500">{error}</div>
        ) : files.length === 0 && folders.length === 0 && currentPath === '/' && !currentPortal ? (
          <div className="text-center p-12 text-gray-500">当前目录没有文件</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    文件名
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    类型
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    大小
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    上传时间
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    同步状态
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {/* 添加返回上一级目录项 */}
                {(currentPath !== '/' || currentPortal) && (
                  <tr className="bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                          <img 
                            src="/static/file-types/folder-up.svg"
                            alt="return"
                            className="h-8 w-8"
                          />
                        </div>
                        <div className="ml-4">
                          <button 
                            onClick={navigateUp}
                            className="text-sm font-medium text-blue-600 hover:text-blue-800"
                          >
                            ..返回上一级
                          </button>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">目录</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">--</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">--</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      --
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button 
                        onClick={navigateUp}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        返回
                      </button>
                    </td>
                  </tr>
                )}
                {/* 先渲染文件夹 */}
                {folders.map((folder) => {
                  return (
                    <tr key={folder.id} className="bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                            <img 
                              src={folder.isPortal ? "/static/file-types/dynamic-portal.svg" : "/static/file-types/folder.svg"}
                              alt="folder"
                              className="h-8 w-8"
                            />
                          </div>
                          <div className="ml-4">
                            <button 
                              onClick={() => navigateToFolder(folder)}
                              className="text-sm font-medium text-blue-600 hover:text-blue-800"
                            >
                              {folder.name}
                            </button>
                            {folder.isPortal && (
                              <div className="text-xs text-gray-500 mt-0.5">
                                上传入口
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {folder.isPortal ? '上传入口' : '文件夹'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatFileSize(folder.size)} ({folder.fileCount || 0}个文件)
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatUploadTime(folder.createdAt)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span 
                          onClick={() => {
                            if (!folder.isPortal) {
                              const currentStatus = syncStatuses[folder.path] || '未同步';
                              const newStatus = currentStatus === '未同步' ? '已同步' : '未同步';
                              handleChangeStatus(folder, newStatus);
                            }
                          }}
                          className={folder.isPortal ? 
                            "inline-block px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" :
                            getStatusStyle(syncStatuses[folder.path] || '未同步')
                          }
                          title={folder.isPortal ? "上传入口" : "点击切换状态"}
                        >
                          {folder.isPortal ? '上传入口' : (syncStatuses[folder.path] || '未同步')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button 
                          onClick={() => navigateToFolder(folder)}
                          className="text-blue-600 hover:text-blue-900 mr-4"
                        >
                          打开
                        </button>
                        {!folder.isPortal && (
                          <button 
                            onClick={() => handleDeleteItem(folder)}
                            className="text-red-600 hover:text-red-900"
                          >
                            删除
                          </button>
                        )}
                      </td>
                    </tr>
                  )
                })}
                {/* 然后渲染文件 */}
                {files.map((file) => {
                  return (
                    <tr key={file.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                            <img 
                              src={getFileIcon(file)}
                              alt={file.type}
                              className="h-8 w-8 object-cover rounded"
                              onError={(e) => {
                                e.target.onerror = null
                                e.target.src = "/static/file-types/default.svg"
                              }}
                            />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {file.name || file.filename}
                            </div>
                            <div className="text-xs text-gray-500 mt-0.5">
                              {file.path}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {file.type.toUpperCase()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatFileSize(file.size)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatUploadTime(file.createdAt || file.uploaded)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span 
                          onClick={() => {
                            const currentStatus = syncStatuses[file.path] || file.syncStatus || '未同步';
                            const newStatus = currentStatus === '未同步' ? '已同步' : '未同步';
                            handleChangeStatus(file, newStatus);
                          }}
                          className={getStatusStyle(syncStatuses[file.path] || file.syncStatus || '未同步')}
                          title="点击切换状态"
                        >
                          {syncStatuses[file.path] || file.syncStatus || '未同步'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button 
                          onClick={() => handlePreviewFile(file)}
                          className="text-blue-600 hover:text-blue-900 mr-4"
                        >
                          预览
                        </button>
                        <button
                          onClick={() => handleDownloadFile(file)}
                          className="text-green-600 hover:text-green-900 mr-4"
                        >
                          下载
                        </button>
                        <button 
                          onClick={() => handleDeleteItem(file)}
                          className="text-red-600 hover:text-red-900"
                        >
                          删除
                        </button>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* 新建文件夹模态框 */}
      {showNewFolderModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">新建文件夹</h3>
            <form onSubmit={handleCreateFolder}>
              <div className="mb-4">
                <label htmlFor="folderName" className="block text-sm font-medium text-gray-700 mb-2">
                  文件夹名称
                </label>
                <input
                  type="text"
                  id="folderName"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入文件夹名称"
                  required
                />
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowNewFolderModal(false)
                    setNewFolderName('')
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  disabled={creatingFolder}
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-300"
                  disabled={creatingFolder}
                >
                  {creatingFolder ? '创建中...' : '创建'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 文件预览模态框 */}
      {showPreviewModal && previewFile && (
        <div className="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-semibold truncate">{previewFile.filename}</h3>
              <button 
                onClick={closePreview}
                className="text-gray-500 hover:text-gray-700 focus:outline-none"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="p-4 flex-1 overflow-auto">
              {canPreviewInBrowser(previewFile) ? (
                previewFile.type.match(/^(jpg|jpeg|png|gif|webp|svg)$/i) ? (
                  <img 
                    src={getPreviewUrl(previewFile)} 
                    alt={previewFile.filename}
                    className="max-w-full max-h-[70vh] mx-auto"
                  />
                ) : previewFile.type.match(/^(mp4|webm)$/i) ? (
                  <video 
                    controls 
                    className="max-w-full max-h-[70vh] mx-auto"
                  >
                    <source src={getPreviewUrl(previewFile)} type={`video/${previewFile.type}`} />
                    您的浏览器不支持视频标签。
                  </video>
                ) : previewFile.type.match(/^(mp3|wav|ogg)$/i) ? (
                  <audio 
                    controls 
                    className="w-full"
                  >
                    <source src={getPreviewUrl(previewFile)} type={`audio/${previewFile.type}`} />
                    您的浏览器不支持音频标签。
                  </audio>
                ) : previewFile.type === 'pdf' ? (
                  <iframe 
                    src={getPreviewUrl(previewFile)}
                    className="w-full h-[70vh]"
                    title={previewFile.filename}
                  ></iframe>
                ) : previewFile.type === 'txt' ? (
                  <iframe 
                    src={getPreviewUrl(previewFile)}
                    className="w-full h-[70vh]"
                    title={previewFile.filename}
                  ></iframe>
                ) : (
                  <div className="text-center p-12">
                    <p>无法预览该文件类型。</p>
                    <a 
                      href={getPreviewUrl(previewFile)} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-md"
                    >
                      在新标签页中打开
                    </a>
                  </div>
                )
              ) : (
                <div className="text-center p-12">
                  <p>无法预览该文件类型。</p>
                  <a 
                    href={getPreviewUrl(previewFile)} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-md"
                  >
                    在新标签页中打开
                  </a>
                </div>
              )}
            </div>
            
            <div className="p-4 border-t flex justify-between">
              <div className="text-sm text-gray-600">
                <span className="font-semibold">类型:</span> {previewFile.type.toUpperCase() || '未知'} | 
                <span className="font-semibold ml-2">大小:</span> {formatFileSize(previewFile.size)} | 
                <span className="font-semibold ml-2">上传时间:</span> {new Date(previewFile.uploaded).toLocaleString()}
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => handleDownloadFile(previewFile)}
                  className="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  下载
                </button>
                <button
                  onClick={closePreview}
                  className="px-3 py-1 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 