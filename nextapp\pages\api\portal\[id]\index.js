import fs from 'fs'
import path from 'path'
import formidable from 'formidable'
import { v4 as uuidv4 } from 'uuid'
import { UAParser } from 'ua-parser-js'

// 设置上传入口数据存储的目录
const PORTALS_DIR = path.join(process.cwd(), 'data', 'upload-portals')
const PORTALS_FILE = path.join(PORTALS_DIR, 'portals.json')
const UPLOADS_DIR = path.join(process.cwd(), 'uploads', 'portals')

// 确保目录存在
function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true })
  }
}

// 获取所有上传入口
function getPortals() {
  try {
    if (!fs.existsSync(PORTALS_FILE)) {
      return []
    }
    
    const data = fs.readFileSync(PORTALS_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading portals file:', error)
    return []
  }
}

// 获取指定上传入口
function getPortal(id) {
  const portals = getPortals()
  // 首先尝试通过linkId查找
  let portal = portals.find(portal => portal.linkId === id)
  
  // 如果没找到，再尝试通过id查找（向后兼容）
  if (!portal) {
    portal = portals.find(portal => portal.id === id)
  }
  
  return portal
}

// 保存上传入口
function savePortals(portals) {
  try {
    ensureDirectoryExists(PORTALS_DIR)
    fs.writeFileSync(PORTALS_FILE, JSON.stringify(portals, null, 2), 'utf8')
    return true
  } catch (error) {
    console.error('Error saving portals:', error)
    return false
  }
}

// 更新上传入口统计信息
function updatePortalStats(portalId, fileSize = 0, newDevice = false) {
  try {
    const portals = getPortals()
    const index = portals.findIndex(portal => portal.id === portalId)
    
    if (index === -1) {
      return false
    }
    
    // 更新统计信息
    if (newDevice) {
      portals[index].stats.devices += 1
    }
    
    if (fileSize > 0) {
      portals[index].stats.uploads += 1
      portals[index].stats.totalSize += fileSize
    }
    
    return savePortals(portals)
  } catch (error) {
    console.error(`Error updating stats for portal ${portalId}:`, error)
    return false
  }
}

// 获取设备ID
function getDeviceId(req) {
  // 首先尝试从cookie中获取deviceId
  const deviceIdCookie = req.cookies?.deviceId
  
  if (deviceIdCookie) {
    return deviceIdCookie
  }
  
  // 如果没有cookie，则生成一个新的deviceId
  return uuidv4()
}

// 解析用户代理
function parseUserAgent(userAgent) {
  const parser = new UAParser(userAgent)
  const result = parser.getResult()
  
  return {
    browser: `${result.browser.name || 'Unknown'} ${result.browser.version || ''}`,
    os: `${result.os.name || 'Unknown'} ${result.os.version || ''}`,
    device: result.device.vendor 
      ? `${result.device.vendor} ${result.device.model || ''}`
      : (result.device.type || 'Desktop')
  }
}

export const config = {
  api: {
    bodyParser: false,
  },
}

export default async function handler(req, res) {
  try {
    // 获取上传入口ID
    const { id } = req.query
    
    if (!id) {
      return res.status(400).json({ error: '未提供入口ID' })
    }
    
    // 获取上传入口信息
    const portal = getPortal(id)
    
    if (!portal) {
      return res.status(404).json({ error: '上传入口不存在' })
    }
    
    // 检查上传入口是否过期
    if (portal.expiresAt && new Date(portal.expiresAt) < new Date()) {
      return res.status(410).json({ error: '上传入口已过期' })
    }
    
    // 检查入口是否被禁用
    if (portal.disabled) {
      return res.status(403).json({ error: '上传入口已被禁用' })
    }
    
    // 获取设备ID
    const deviceId = getDeviceId(req)
    
    // 设置设备ID cookie (7天过期)
    res.setHeader('Set-Cookie', `deviceId=${deviceId}; Path=/; Max-Age=${60*60*24*7}; SameSite=Lax; HttpOnly`)
    
    // 获取入口信息
    if (req.method === 'GET') {
      // 解析用户代理
      const userAgent = parseUserAgent(req.headers['user-agent'] || '')
      
      // 返回入口信息（不包含统计信息）
      return res.status(200).json({
        portal: {
          id: portal.id,
          name: portal.name,
          description: portal.description,
          expiresAt: portal.expiresAt,
          deviceInfo: {
            id: deviceId,
            ...userAgent
          }
        }
      })
    }
    
    // 上传文件
    if (req.method === 'POST') {
      // 确保设备目录存在
      const deviceDir = path.join(UPLOADS_DIR, portal.id, deviceId)
      ensureDirectoryExists(deviceDir)
      
      // 检查设备目录是否为新设备
      const isNewDevice = fs.readdirSync(deviceDir).length === 0
      
      // 处理文件上传
      const form = formidable({
        uploadDir: deviceDir,
        keepExtensions: true,
        maxFileSize: 5 * 1024 * 1024 * 1024, // 5GB
        multiples: true,
        filename: (name, ext, part) => {
          // 保留原始文件名，但确保安全
          const originalName = part.originalFilename || 'unknown'
          const safeName = originalName.replace(/[^a-zA-Z0-9_\-\.]/g, '_')

          const targetPath = path.join(deviceDir, safeName)

          // 检查文件是否已存在
          if (fs.existsSync(targetPath)) {
            // 检查文件大小和修改时间，判断是否为重复上传
            try {
              const existingStats = fs.statSync(targetPath)
              const uploadingFileSize = part.size || 0

              // 如果文件大小相同且是最近创建的（5分钟内），可能是重复上传
              const timeDiff = Date.now() - existingStats.mtime.getTime()
              const isSameSize = existingStats.size === uploadingFileSize
              const isRecent = timeDiff < 5 * 60 * 1000 // 5分钟

              if (isSameSize && isRecent) {
                console.log(`检测到可能的重复上传: ${safeName}, 大小: ${uploadingFileSize}, 时间差: ${timeDiff}ms`)
                // 返回原文件名，但后续处理会跳过实际保存
                return safeName + '.duplicate'
              }
            } catch (statError) {
              console.warn('检查现有文件状态失败:', statError)
            }

            // 如果不是重复上传，添加时间戳
            const timestamp = Date.now()
            const extName = path.extname(safeName)
            const baseName = path.basename(safeName, extName)
            console.log(`文件已存在，重命名: ${safeName} -> ${baseName}_${timestamp}${extName}`)
            return `${baseName}_${timestamp}${extName}`
          }

          return safeName
        }
      })
      
      return new Promise((resolve, reject) => {
        form.parse(req, (err, fields, files) => {
          if (err) {
            console.error('Error parsing form:', err)
            return resolve(res.status(500).json({ error: '文件上传失败' }))
          }
          
          const fileArray = Array.isArray(files.file) ? files.file : [files.file]
          
          if (!fileArray.length || !fileArray[0]) {
            return resolve(res.status(400).json({ error: '没有上传文件' }))
          }
          
          const uploadedFiles = fileArray.filter(file => {
            // 跳过标记为重复的文件
            if (file.newFilename && file.newFilename.endsWith('.duplicate')) {
              console.log(`跳过重复文件: ${file.originalFilename}`)
              return false
            }
            return true
          }).map(file => {
            // 更新统计信息
            updatePortalStats(portal.id, file.size, isNewDevice && fileArray.indexOf(file) === 0)

            return {
              name: file.originalFilename,
              size: file.size,
              path: path.basename(file.filepath),
              type: file.mimetype || 'application/octet-stream'
            }
          })
          
          resolve(res.status(200).json({ files: uploadedFiles }))
        })
      })
    }
    
    // 不支持的方法
    return res.status(405).json({ error: '不支持的请求方法' })
  } catch (error) {
    console.error('Error handling request:', error)
    return res.status(500).json({ error: '服务器内部错误' })
  }
} 