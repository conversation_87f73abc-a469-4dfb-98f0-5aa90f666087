// 文件云流转助手 - 内容脚本
// 处理文件云流转页面中的文件夹创建和文件上传操作

// 全局变量
let pendingFiles = [];
let todayFolderName = '';
let isDebugMode = true; // 启用调试模式

// 调试函数
function debug(message) {
  if (isDebugMode) {
    console.log(`[文件云流转助手] ${message}`);
  }
}

// 等待元素出现
async function waitForElement(selector, timeoutMs = 5000) {
  debug(`等待元素出现: ${selector}`);
  
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeoutMs) {
    const element = document.querySelector(selector);
    if (element) {
      debug(`元素已出现: ${selector}`);
      return element;
    }
    await sleep(100);
  }
  
  debug(`等待元素出现超时: ${selector}`);
  throw new Error(`等待元素出现超时: ${selector}`);
}

// 等待指定时间
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 获取今天日期格式化为文件夹名
function getTodayFolderName() {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  
  // 返回格式化的日期 - YYYYMMDD
  return `${year}${month}${day}`;
}

// 检查今日文件夹是否存在
async function checkTodayFolderExists() {
  debug('检查今日文件夹是否存在');
  
  try {
    // 获取今天的日期格式
    const folderName = getTodayFolderName();
    debug(`检查文件夹: ${folderName}`);
    
    // 等待文件列表加载
    const fileListSelector = '.file-list-container'; // 实际页面中的选择器
    await waitForElement(fileListSelector, 5000);
    
    debug('文件列表已加载，检查是否有今日文件夹');
    const fileList = document.querySelector(fileListSelector);
    if (!fileList) {
      debug('未找到文件列表元素');
      return false;
    }
    
    // 检查文件列表中是否存在今日文件夹
    // 使用XPath查找包含文件夹名的元素
    const xpathResult = document.evaluate(
      `//*[contains(text(), '${folderName}')]`,
      fileList,
      null,
      XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
      null
    );
    
    const count = xpathResult.snapshotLength;
    debug(`找到 ${count} 个匹配的元素`);
    
    if (count > 0) {
      debug('找到今日文件夹');
      return true;
    }
    
    // 检查是否有列表项但没有文件夹
    const hasEmptyIndicator = document.querySelector('.has-no-folder');
    if (hasEmptyIndicator) {
      debug('文件夹列表为空');
      return false;
    }
    
    // 记录文件列表中存在的元素名称
    const fileItems = fileList.querySelectorAll('.file-list-item');
    if (fileItems.length > 0) {
      debug(`文件列表中有 ${fileItems.length} 个项目`);
      for (let i = 0; i < Math.min(fileItems.length, 5); i++) {
        const nameEl = fileItems[i].querySelector('.file-list-name');
        if (nameEl) {
          debug(`列表项 ${i+1}: ${nameEl.textContent.trim()}`);
        }
      }
    }
    
    debug('未找到今日文件夹');
    return false;
  } catch (error) {
    debug(`检查今日文件夹时出错: ${error.message}`);
    return false;
  }
}

// 检查当前是否在文件云页面
async function isFileCloudPage() {
  debug('检查当前是否在文件云页面');
  
  try {
    // 首先检查页面是否为错误页面
    if (document.title.includes('405') || document.body.textContent.includes('405 Not Allowed')) {
      debug('当前是405错误页面');
      return false;
    }
    
    // 尝试查找文件云页面的特征元素
    const fileCloudIndicators = [
      '.file-list-container',
      '.file-list-box',
      '.common-file-list-content',
      '.file-cloud',
      '.file-manager',
      '.cloud-drive',
      '[data-page="file-cloud"]',
      '[data-module="file-system"]'
    ];
    
    // 检查特征元素
    for (const selector of fileCloudIndicators) {
      if (document.querySelector(selector)) {
        debug(`找到文件云特征元素: ${selector}`);
        return true;
      }
    }
    
    // 检查URL是否包含文件云相关的路径
    const url = window.location.href.toLowerCase();
    const urlPatterns = [
      'files',
      'cloud',
      'drive',
      'storage',
      'document'
    ];
    
    // 判断URL中是否包含相关关键词
    const urlMatches = urlPatterns.some(pattern => url.includes(pattern));
    if (urlMatches) {
      debug('URL匹配文件云页面');
      return true;
    }
    
    // 根据页面标题判断
    const pageTitle = document.title.toLowerCase();
    if (pageTitle.includes('文件') || 
        pageTitle.includes('云') || 
        pageTitle.includes('cloud') || 
        pageTitle.includes('files')) {
      debug('页面标题匹配文件云页面');
      return true;
    }
    
    debug('当前不是文件云页面');
    return false;
  } catch (error) {
    debug(`检查文件云页面时出错: ${error.message}`);
    return false;
  }
}

// 等待应用加载完成
async function waitForAppReady() {
  debug('等待应用加载完成');
  
  try {
    // 等待加载指示器消失
    const loadingSelectors = [
      '.loading',
      '.spinner',
      '.loading-indicator',
      '[data-role="loading"]',
      '.progress-indicator'
    ];
    
    // 等待任意一个加载指示器消失
    for (const selector of loadingSelectors) {
      const loadingElement = document.querySelector(selector);
      if (loadingElement) {
        debug(`发现加载指示器: ${selector}，等待其消失`);
        await waitForElementToDisappear(selector, 10000);
      }
    }
    
    // 等待文件列表或主内容区域出现 - 根据实际页面结构调整选择器
    const contentSelectors = [
      '.file-list-container',
      '.file-list-box',
      '.common-file-list-content',
      '.file-right',
      '.file-attr-container',
      '.content-area',
      '.main-content'
    ];
    
    // 等待任意一个内容区域出现
    let contentFound = false;
    for (const selector of contentSelectors) {
      if (document.querySelector(selector)) {
        debug(`找到内容区域: ${selector}`);
        contentFound = true;
        break;
      }
    }
    
    // 如果没有找到内容区域，等待第一个选择器出现
    if (!contentFound) {
      debug(`等待内容区域出现: ${contentSelectors[0]}`);
      await waitForElement(contentSelectors[0], 10000);
    }
    
    // 额外等待一小段时间，确保应用完全加载
    await sleep(1000);
    
    debug('应用加载完成');
    return true;
  } catch (error) {
    debug(`等待应用加载时出错: ${error.message}`);
    return false;
  }
}

// 记录页面状态用于故障排除
function recordPageState() {
  debug('记录当前页面状态');
  
  try {
    // 记录页面URL
    debug(`当前URL: ${window.location.href}`);
    
    // 记录页面标题
    debug(`页面标题: ${document.title}`);
    
    // 记录可见按钮
    const buttons = document.querySelectorAll('button, [role="button"], .btn');
    const buttonTexts = Array.from(buttons)
      .map(btn => btn.textContent.trim())
      .filter(text => text.length > 0);
    debug(`可见按钮: ${buttonTexts.join(', ')}`);
    
    // 记录导航元素
    const navItems = document.querySelectorAll('nav a, .navigation, .nav-item');
    const navTexts = Array.from(navItems)
      .map(nav => nav.textContent.trim())
      .filter(text => text.length > 0);
    debug(`导航元素: ${navTexts.join(', ')}`);
    
    return true;
  } catch (error) {
    debug(`记录页面状态时出错: ${error.message}`);
    return false;
  }
}

// 监听来自上传页面的消息
window.addEventListener('message', function(event) {
  try {
    debug(`收到message事件: ${JSON.stringify(event.data)}`);
    const { type, data } = event.data;
    
    if (type === 'NEW_FILE') {
      debug(`收到新文件消息: ${JSON.stringify(data)}`);
      handleNewFile(data);
    }
  } catch (error) {
    debug(`处理消息出错: ${error.message}`);
  }
});

// 处理新文件通知
async function handleNewFile(fileData) {
  debug(`处理新文件: ${fileData.filename}`);
  
  // 将文件添加到待处理队列
  pendingFiles.push(fileData);
  
  // 通知background.js处理新文件
  try {
    await chrome.runtime.sendMessage({
      type: 'NEW_FILE_RECEIVED',
      data: fileData
    });
    
    // 如果当前是文件云流转页面，自动开始处理
    if (isFileCloudPage()) {
      debug('当前是文件云流转页面，开始处理文件队列');
      processFilesQueue();
    } else {
      debug('当前不是文件云流转页面，请求打开文件云流转页面');
      chrome.runtime.sendMessage({ type: 'OPEN_FILE_CLOUD_PAGE' });
    }
  } catch (error) {
    debug(`发送消息失败: ${error.message}`);
  }
}

// 查找文本节点
function findTextInNodes(node, searchText) {
  if (node.nodeType === Node.TEXT_NODE) {
    if (node.textContent.trim() === searchText) {
      return { found: true, element: node.parentElement };
    }
    return { found: false };
  }
  
  if (!node.childNodes || node.childNodes.length === 0) {
    return { found: false };
  }
  
  for (const child of node.childNodes) {
    const result = findTextInNodes(child, searchText);
    if (result.found) {
      return result;
    }
  }
  
  return { found: false };
}

// 主处理流程
async function processFilesWorkflow() {
  debug('开始处理文件上传流程');
  
  if (pendingFiles.length === 0) {
    debug('没有待处理的文件，流程结束');
    return;
  }
  
  try {
    // 确保应用已准备就绪
    debug('等待应用准备就绪');
    await waitForAppReady();
    
    // 检查当前是否在正确的页面
    if (!isFileCloudPage()) {
      debug('当前不是文件云页面，无法处理上传');
      showNotification('请确保在正确的文件云页面进行操作');
      return;
    }
    
    // 记录页面状态，帮助调试
    recordPageState();
    
    // 检查今日文件夹是否存在
    debug('检查今日文件夹是否存在');
    const folderExists = await checkTodayFolderExists();
    debug(`今日文件夹是否存在: ${folderExists}`);
    
    if (!folderExists) {
      debug('需要创建今日文件夹');
      // 创建今日文件夹
      const created = await createTodayFolder();
      
      if (!created) {
        debug('创建文件夹失败，中止流程');
        showNotification('创建文件夹失败，请手动创建或刷新页面重试');
        return;
      }
    }
    
    // 进入今日文件夹
    debug('尝试进入今日文件夹');
    const entered = await enterTodayFolder();
    
    if (!entered) {
      debug('进入文件夹失败，中止流程');
      showNotification('进入文件夹失败，请手动进入后重试');
      return;
    }
    
    // 上传文件
    debug('开始上传文件');
    await uploadPendingFiles();
    
    debug('文件处理流程完成');
  } catch (error) {
    debug(`文件处理流程出错: ${error.message}`);
    showNotification('文件上传过程中出错，请刷新页面重试');
  }
}

// 页面初始化处理
function initPage() {
  debug('页面初始化');
  
  if (!isFileCloudPage()) {
    debug('当前页面不是文件云页面，跳过初始化');
    return;
  }
  
  // 记录页面状态
  recordPageState();
  
  debug('设置消息监听器');
  // 监听来自background.js的消息
  chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
    debug(`收到消息: ${JSON.stringify(request)}`);
    
    // 检查是否在云盘页面，如果是则不处理，让云盘专用脚本处理
    if (window.location.href.includes('yunpan.gdcourts.gov.cn')) {
      debug('在云盘页面，跳过content.js处理，让云盘专用脚本处理');
      return false; // 不处理此消息
    }
    
    // 立即发送响应，表明我们会处理这个请求
    sendResponse({ status: 'processing' });
    
    if (request.type === 'START_UPLOAD_PROCESS') {
      debug('收到开始上传处理的消息');
      
      // 检查页面状态并准备操作
      debug('检查页面状态并准备操作...');
      const isFilePage = await isFileCloudPage();
      debug(`是否是文件云页面: ${isFilePage}`);
      
      if (isFilePage) {
        debug('页面就绪，等待后续文件上传操作');
        
        // 记录页面状态以便调试
        recordPageState();
        
        // 获取真实待上传文件列表
        const pendingFiles = await getFilesFromPopup();
        
        if (pendingFiles.length === 0) {
          debug('没有待上传的文件');
          chrome.runtime.sendMessage({
            type: 'UPLOAD_RESULT',
            success: false,
            message: '没有待上传的文件'
          });
        } else {
          debug(`开始处理 ${pendingFiles.length} 个待上传文件`);
          
          // 处理每个文件
          for (const pendingFile of pendingFiles) {
            if (!pendingFile.status || pendingFile.status === 'pending') {
              try {
                // 不再尝试从服务器获取文件内容，改为请求background.js提供文件数据
                debug(`准备获取文件: ${pendingFile.filename}`);
                
                // 从background获取文件数据
                const fileData = await getFileDataFromBackground(pendingFile.filename);
                
                if (!fileData || !fileData.blob) {
                  throw new Error('无法获取文件数据');
                }
                
                debug(`成功获取文件数据，大小: ${fileData.blob.size} 字节`);
                
                const fileName = pendingFile.originalname || pendingFile.filename;
                
                // 创建File对象
                const file = new File([fileData.blob], fileName, { 
                  type: pendingFile.mimetype || fileData.blob.type || 'application/octet-stream',
                  lastModified: new Date().getTime()
                });
                
                debug(`准备上传文件: ${fileName}, 大小: ${file.size} 字节`);
                
                // 处理文件上传 - 直接传递File对象
                const result = await uploadFile(fileName, file);
                
                if (result) {
                  debug(`文件 ${fileName} 上传流程完成`);
                  chrome.runtime.sendMessage({
                    type: 'UPLOAD_COMPLETED',
                    success: true,
                    fileName: pendingFile.filename
                  });
                } else {
                  debug(`文件 ${fileName} 上传流程失败`);
                  chrome.runtime.sendMessage({
                    type: 'UPLOAD_COMPLETED',
                    success: false,
                    fileName: pendingFile.filename,
                    error: '上传失败'
                  });
                }
              } catch (error) {
                debug(`处理文件 ${pendingFile.filename} 上传时出错: ${error.message}`);
                chrome.runtime.sendMessage({
                  type: 'UPLOAD_COMPLETED',
                  success: false,
                  fileName: pendingFile.filename,
                  error: error.message
                });
              }
            }
          }
        }
        
        // 发送页面就绪消息
        chrome.runtime.sendMessage({
          type: 'PAGE_READY',
          pageType: 'fileCloud'
        });
        debug('已发送页面就绪消息');
      } else {
        debug('当前不是文件云页面，无法进行上传操作');
        chrome.runtime.sendMessage({
          type: 'PAGE_ERROR',
          error: '当前不是文件云页面'
        });
      }
    }
  });
  
  debug('页面初始化完成');
}

// 在页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
  debug('DOM已加载，初始化页面');
  setTimeout(initPage, 1000); // 短暂延迟确保DOM完全加载
});

// 在页面完全加载后再次检查
window.addEventListener('load', () => {
  debug('页面完全加载，再次初始化');
  setTimeout(initPage, 2000); // 延迟确保所有资源都已加载
});

// 监听页面状态变化（如导航、SPA路由变化等）
let lastUrl = location.href;
const urlObserver = new MutationObserver(() => {
  if (location.href !== lastUrl) {
    lastUrl = location.href;
    debug(`URL已变更为: ${lastUrl}`);
    
    // 检查是否是文件云页面
    if (isFileCloudPage()) {
      debug('导航到了文件云页面，初始化');
      setTimeout(initPage, 1000);
    }
  }
});

// 开始观察URL变化
urlObserver.observe(document, { subtree: true, childList: true });

// 替换原来的processFilesQueue函数
function processFilesQueue() {
  debug(`处理文件队列，待处理文件数: ${pendingFiles.length}`);
  
  if (pendingFiles.length === 0) {
    debug('没有待处理的文件');
    return;
  }
  
  // 检查是否在文件云页面
  if (!isFileCloudPage()) {
    debug('当前不在文件云页面，无法处理文件上传');
    showNotification('请在文件云页面中处理文件上传');
    return;
  }
  
  // 执行文件处理流程
  processFilesWorkflow();
}

// 找到上传按钮并点击
function findUploadButton() {
  debug('查找上传按钮');
  
  // 首先尝试使用提供的选择器
  const specificSelector = '.uppy-FileInput-btn.uploadBtn';
  const specificBtn = document.querySelector(specificSelector);
  
  if (specificBtn) {
    debug(`使用特定选择器找到上传按钮: ${specificSelector}`);
    return specificBtn;
  }
  
  // 尝试使用完整路径
  const fullPathSelector = '#uploadBoxIsHidden > div > app-upload > div:nth-child(1) > div.UppyForm > div > button.uppy-FileInput-btn.uploadBtn';
  const fullPathBtn = document.querySelector(fullPathSelector);
  
  if (fullPathBtn) {
    debug(`使用完整路径选择器找到上传按钮: ${fullPathSelector}`);
    return fullPathBtn;
  }
  
  // 尝试查找任何包含"上传文件"文本的按钮
  const textButtons = Array.from(document.querySelectorAll('button')).filter(
    btn => btn.textContent && btn.textContent.trim() === '上传文件'
  );
  
  if (textButtons.length > 0) {
    debug(`找到${textButtons.length}个包含"上传文件"文本的按钮`);
    return textButtons[0];
  }
  
  // 最后尝试查找任何包含"上传"文本的按钮
  const uploadButtons = Array.from(document.querySelectorAll('button')).filter(
    btn => btn.textContent && btn.textContent.includes('上传') && !btn.textContent.includes('上传文件夹')
  );
  
  if (uploadButtons.length > 0) {
    debug(`找到${uploadButtons.length}个包含"上传"文本的按钮`);
    return uploadButtons[0];
  }
  
  debug('未找到上传按钮');
  return null;
}

// 触发上传按钮点击
async function clickUploadButton() {
  const btn = findUploadButton();
  if (!btn) {
    throw new Error('未找到上传按钮');
  }
  
  debug('点击上传按钮');
  
  // 尝试点击
  try {
    // 检查按钮是否可见和可点击
    if (btn.offsetParent === null) {
      throw new Error('上传按钮不可见');
    }
    
    // 创建并分发点击事件
    btn.click();
    
    // 额外尝试使用事件分发
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    btn.dispatchEvent(clickEvent);
    
    debug('已点击上传按钮');
    return true;
  } catch (error) {
    debug(`点击上传按钮时出错: ${error.message}`);
    return false;
  }
}

// 创建今日文件夹
async function createTodayFolder() {
  debug('开始创建今日文件夹');
  
  try {
    // 使用提供的选择器查找新建按钮
    const newBtnSelector = '.new-btn.button_grey_hover.flex.flexAlignCenter.flexCenter.flexShrink';
    const newBtn = document.querySelector(newBtnSelector);
    
    if (!newBtn) {
      debug('未找到新建按钮，尝试使用XPath');
      
      // 尝试使用更复杂的选择器路径
      const complexSelector = 'body > app-root > div > home > div > div > main > div.router-content > file-personal > div > div > main > div:nth-child(1) > div > function-button > div > div.fn-btn.flex.flexAlignCenter > div.new-btn';
      const complexBtn = document.querySelector(complexSelector);
      
      if (complexBtn) {
        debug('使用复杂选择器找到新建按钮');
        newBtn = complexBtn;
      } else {
        // 尝试查找任何包含"新建"文本的按钮或span元素
        const newBtnElements = Array.from(document.querySelectorAll('button, span, div')).filter(
          el => el.textContent && el.textContent.includes('新建')
        );
        
        if (newBtnElements.length > 0) {
          debug(`找到${newBtnElements.length}个包含"新建"文本的元素，使用第一个`);
          const firstNewBtn = newBtnElements[0];
          debug(`按钮文本: ${firstNewBtn.textContent.trim()}`);
          debug(`按钮标签: ${firstNewBtn.tagName}`);
          debug(`按钮类名: ${firstNewBtn.className}`);
          
          // 点击找到的新建按钮
          firstNewBtn.click();
          debug('已点击新建按钮');
        } else {
          debug('未找到任何包含"新建"文本的元素');
          return false;
        }
      }
    } else {
      debug('找到新建按钮，点击它');
      newBtn.click();
      debug('已点击新建按钮');
    }
    
    // 等待新建菜单出现
    await sleep(1000);
    
    // 查找文件夹选项 - 查找可能的选项
    debug('查找文件夹选项');
    
    // 查找文件夹选项
    let folderMenuItem = null;
    
    // 尝试方式1: 使用选择器查找下拉菜单中的第一个选项
    const menuItems = document.querySelectorAll('.option-list > div, .ant-dropdown-menu-item, [class*="dropdown"] > div, [class*="menu-item"]');
    if (menuItems.length > 0) {
      debug(`找到 ${menuItems.length} 个可能的菜单项`);
      
      // 通常第一个选项是文件夹
      folderMenuItem = menuItems[0];
      debug(`选择第一个菜单项: ${folderMenuItem.textContent.trim()}`);
      
      // 点击文件夹选项
      folderMenuItem.click();
      debug('已点击文件夹菜单项');
    } else {
      // 尝试方式2: 通过XPath查找包含"文件夹"的可点击元素
      const folderXPath = "//*[contains(text(), '文件夹') and not(contains(text(), '上传文件夹'))]";
      const folderResult = document.evaluate(
        folderXPath,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null
      );
      
      if (folderResult.snapshotLength > 0) {
        debug(`通过XPath找到 ${folderResult.snapshotLength} 个文件夹相关元素`);
        
        // 使用第一个元素
        const folderElement = folderResult.snapshotItem(0);
        debug(`选择文件夹元素: ${folderElement.textContent.trim()}`);
        
        // 尝试点击此元素
        folderElement.click();
        debug('已点击文件夹XPath元素');
      } else {
        debug('未找到文件夹选项，尝试重新点击新建按钮查看下拉菜单');
        
        // 再次点击新建按钮
        if (newBtn) {
          newBtn.click();
          debug('再次点击新建按钮');
          
          // 等待菜单出现
          await sleep(1000);
          
          // 根据按钮文字尝试直接查找"文件夹"按钮
          const folderButtons = Array.from(document.querySelectorAll('button, span, div')).filter(
            el => el.offsetParent !== null && 
            el.textContent && 
            (el.textContent.includes('文件夹') && !el.textContent.includes('上传文件夹'))
          );
          
          if (folderButtons.length > 0) {
            debug(`找到 ${folderButtons.length} 个文件夹按钮`);
            
            // 使用第一个按钮
            const folderBtn = folderButtons[0];
            debug(`选择文件夹按钮: ${folderBtn.textContent.trim()}`);
            
            // 点击文件夹按钮
            folderBtn.click();
            debug('已点击文件夹按钮');
          } else {
            debug('无法找到任何文件夹相关按钮');
            return false;
          }
        } else {
          debug('无法再次点击新建按钮');
          return false;
        }
      }
    }
    
    // 等待对话框出现
    debug('等待文件夹创建对话框出现');
    await sleep(1500);
    
    // 获取今日文件夹名称
    const folderName = getTodayFolderName();
    debug(`设置文件夹名称: ${folderName}`);
    
    // 查找文件夹名称输入框 - 首先尝试用户提供的精确选择器
    debug('查找文件夹名称输入框');
    const exactSelectors = [
      '#cdk-overlay-20 > nz-modal > div > div.ant-modal-wrap > div > div > div.ant-modal-body.ng-star-inserted > div > div > input',
      '.ant-modal-body input',
      '.ant-modal-content input',
      '.modal-dialog input',
      'nz-modal input',
      '[role="dialog"] input'
    ];
    
    let folderNameInput = null;
    
    // 尝试使用精确选择器
    for (const selector of exactSelectors) {
      debug(`尝试精确选择器: ${selector}`);
      const element = document.querySelector(selector);
      if (element) {
        debug(`找到输入框通过选择器: ${selector}`);
        folderNameInput = element;
        break;
      }
    }
    
    // 如果没找到，尝试通过对话框查找所有输入框
    if (!folderNameInput) {
      debug('通过精确选择器未找到输入框，尝试查找模态对话框中的所有输入框');
      
      // 查找可见的模态对话框
      const modalElements = document.querySelectorAll('.ant-modal, .modal, nz-modal, [role="dialog"], .dialog-box, .modal-content, .modal-body');
      debug(`找到 ${modalElements.length} 个可能的模态对话框元素`);
      
      // 针对每个对话框查找输入框
      for (const modal of modalElements) {
        const inputs = modal.querySelectorAll('input');
        debug(`在模态框中找到 ${inputs.length} 个输入框`);
        
        if (inputs.length > 0) {
          for (const input of inputs) {
            if (input.offsetParent !== null) {
              folderNameInput = input;
              debug(`选择模态框中的输入框: ${input.outerHTML.substring(0, 100)}`);
              break;
            }
          }
          
          if (folderNameInput) break;
        }
      }
    }
    
    // 如果仍未找到，尝试查找页面上任何可见的输入框
    if (!folderNameInput) {
      debug('未能在对话框中找到输入框，尝试查找页面上任何可见的输入框');
      
      // 查找所有可见的输入框
      const allInputs = Array.from(document.querySelectorAll('input[type="text"]')).filter(
        input => input.offsetParent !== null && (!input.placeholder || !input.placeholder.includes('搜索'))
      );
      
      debug(`找到 ${allInputs.length} 个可见的非搜索输入框`);
      
      // 检查输入框的属性，寻找可能的匹配
      for (const input of allInputs) {
        debug(`检查输入框: ${input.outerHTML.substring(0, 100)}`);
        
        // 简单的启发式，选择最可能是创建文件夹的输入框
        if (!input.placeholder || !input.placeholder.includes('搜索')) {
          folderNameInput = input;
          debug(`选择可能的输入框: ${input.outerHTML.substring(0, 100)}`);
          break;
        }
      }
      
      if (!folderNameInput && allInputs.length > 0) {
        debug('没有找到匹配的输入框，使用第一个可见的输入框');
        folderNameInput = allInputs[0];
      }
    }
    
    // 如果找不到输入框，尝试查找可编辑的div
    if (!folderNameInput) {
      debug('未找到输入框，尝试查找可编辑的div或span元素');
      
      const editableElements = Array.from(document.querySelectorAll('[contenteditable="true"], [role="textbox"]')).filter(
        el => el.offsetParent !== null
      );
      
      if (editableElements.length > 0) {
        debug(`找到 ${editableElements.length} 个可编辑元素`);
        const editableElement = editableElements[0];
        
        // 使用这个可编辑元素
        debug(`使用可编辑元素: ${editableElement.outerHTML.substring(0, 100)}`);
        
        // 清空内容
        editableElement.textContent = '';
        
        // 聚焦元素
        editableElement.focus();
        
        // 输入文件夹名称
        editableElement.textContent = folderName;
        
        // 触发输入事件
        editableElement.dispatchEvent(new Event('input', { bubbles: true }));
        editableElement.dispatchEvent(new Event('change', { bubbles: true }));
        
        debug('已设置文件夹名称到可编辑元素');
      } else {
        debug('未找到任何可用的输入元素');
        return false;
      }
    } else {
      // 清空输入框并设置文件夹名称
      debug(`设置文件夹名称到输入框: ${folderName}`);
      folderNameInput.value = '';
      folderNameInput.focus();
      folderNameInput.value = folderName;
      
      // 触发输入事件
      folderNameInput.dispatchEvent(new Event('input', { bubbles: true }));
      folderNameInput.dispatchEvent(new Event('change', { bubbles: true }));
      
      debug('已设置文件夹名称到输入框');
    }
    
    // 等待确保输入完成
    await sleep(500);
    
    // 查找确认按钮
    debug('查找确认按钮');
    
    // 尝试查找确认按钮
    let confirmButton = null;
    
    // 首先尝试使用用户提供的精确选择器
    const exactConfirmSelector = '#cdk-overlay-20 > nz-modal > div > div.ant-modal-wrap > div > div > div.ant-modal-footer.ng-star-inserted > button.ant-btn.ng-star-inserted.ant-btn-primary';
    confirmButton = document.querySelector(exactConfirmSelector);
    
    if (confirmButton) {
      debug(`使用精确选择器找到确认按钮: ${exactConfirmSelector}`);
    } else {
      // 尝试更简单的ant-modal-footer选择器
      const simpleSelectors = [
        '.ant-modal-footer button.ant-btn-primary',
        '.ant-modal-footer .ant-btn.ant-btn-primary',
        'nz-modal .ant-btn-primary',
        '.ant-modal .ant-btn-primary',
        'button.ant-btn-primary'
      ];

      for (const selector of simpleSelectors) {
        debug(`尝试选择器: ${selector}`);
        confirmButton = document.querySelector(selector);
        if (confirmButton) {
          debug(`使用选择器 ${selector} 找到确认按钮`);
          break;
        }
      }
      
      // 如果还没找到，尝试通过文本内容查找确认按钮
      if (!confirmButton) {
        debug('通过选择器未找到确认按钮，尝试通过文本查找');
        
        // 尝试通过文本内容查找确认按钮
        const confirmTexts = ['确定', '确认', 'OK', '创建', '提交'];
        
        for (const text of confirmTexts) {
          debug(`查找文本为"${text}"的按钮`);
          
          // 使用XPath查找
          const xpath = `//*[self::button or self::a or @role="button"][contains(text(), "${text}")]`;
          const result = document.evaluate(
            xpath,
            document,
            null,
            XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
            null
          );
          
          if (result.snapshotLength > 0) {
            confirmButton = result.snapshotItem(0);
            debug(`找到确认按钮，文本: ${confirmButton.textContent.trim()}`);
            break;
          }
        }
        
        // 如果没有找到，尝试在模态框中查找按钮
        if (!confirmButton) {
          debug('未通过文本找到确认按钮，尝试在模态框中查找');
          
          const modals = document.querySelectorAll('.ant-modal, .modal, nz-modal, [role="dialog"], .dialog-box');
          
          for (const modal of modals) {
            const buttons = modal.querySelectorAll('button');
            debug(`在模态框中找到 ${buttons.length} 个按钮`);
            
            if (buttons.length > 0) {
              // 如果有多个按钮，通常最后一个是确认按钮
              confirmButton = buttons[buttons.length - 1];
              debug(`选择模态框中最后一个按钮: ${confirmButton.textContent.trim()}`);
              break;
            }
          }
        }
        
        // 如果还没有找到，尝试在页面中查找任何可能的确认按钮
        if (!confirmButton) {
          debug('未在模态框中找到确认按钮，尝试查找任何可能的确认按钮');
          
          const allButtons = Array.from(document.querySelectorAll('button')).filter(
            btn => btn.offsetParent !== null && !btn.disabled
          );
          
          debug(`找到 ${allButtons.length} 个可见按钮`);
          
          if (allButtons.length > 0) {
            // 在所有按钮中查找可能的确认按钮
            for (const btn of allButtons) {
              const text = btn.textContent.trim().toLowerCase();
              
              if (confirmTexts.some(t => text.includes(t.toLowerCase()))) {
                confirmButton = btn;
                debug(`找到可能的确认按钮: ${btn.textContent.trim()}`);
                break;
              }
            }
            
            // 如果仍没找到，使用最后一个按钮
            if (!confirmButton && allButtons.length > 0) {
              confirmButton = allButtons[allButtons.length - 1];
              debug(`使用最后一个可见按钮: ${confirmButton.textContent.trim()}`);
            }
          }
        }
      }
    }
    
    if (!confirmButton) {
      debug('未找到任何可能的确认按钮');
      return false;
    }
    
    // 点击确认按钮
    debug(`点击确认按钮: ${confirmButton.textContent.trim()}`);
    confirmButton.click();
    debug('已点击确认按钮');
    
    // 等待创建完成
    debug('等待文件夹创建完成');
    await sleep(2000);
    
    // 验证文件夹是否已创建
    const folderExists = await checkTodayFolderExists();
    debug(`验证文件夹创建结果: ${folderExists ? '成功' : '失败'}`);
    
    return folderExists;
  } catch (error) {
    debug(`创建今日文件夹时出错: ${error.message}`);
    return false;
  }
}

// 进入今日文件夹
async function enterTodayFolder() {
  debug('尝试进入今日文件夹');
  
  try {
    // 检查当前页面状态，如果有错误先恢复
    if (document.title.includes('405') || document.body.textContent.includes('405 Not Allowed')) {
      debug('检测到405错误，尝试先恢复页面');
      await handlePageError();
      // 等待页面刷新
      await sleep(2000);
    }
    
    // 获取今日文件夹名称
    const todayFolderName = getTodayFolderName();
    debug(`今日文件夹名称: ${todayFolderName}`);
    
    // 从URL确认我们是否已经在文件夹中
    const currentUrl = window.location.href;
    if (currentUrl.includes(`id=${todayFolderName}`) || document.title.includes(todayFolderName)) {
      debug('根据URL判断已在目标文件夹中');
      return true;
    }
    
    // 首先使用提供的精确选择器路径
    const specificSelectors = [
      `#imagesItem > div > div.name-box > div.file-name.file-list-item.flex.flexAlignCenter > div.file-info`,
      `.file-name-ellipsis[title="${todayFolderName}"]`,
      `.file-name-txt span[title="${todayFolderName}"]`
    ];
    
    let folderElement = null;
    let folderItem = null;
    
    // 尝试使用精确选择器
    for (const selector of specificSelectors) {
      debug(`尝试选择器: ${selector}`);
      const elements = document.querySelectorAll(selector);
      
      if (elements.length > 0) {
        debug(`选择器 ${selector} 找到 ${elements.length} 个元素`);
        
        // 尝试找到包含今日日期的元素
        for (const el of elements) {
          if (el.textContent.includes(todayFolderName)) {
            folderElement = el;
            debug(`找到包含今日日期的元素: ${el.outerHTML.substring(0, 100)}`);
            
            // 为了确定要点击的更大元素，向上查找到list-item
            let parent = el;
            for (let i = 0; i < 5; i++) {
              if (!parent) break;
              parent = parent.parentElement;
              if (parent && (
                  parent.classList.contains('list-item') || 
                  parent.classList.contains('file-item') ||
                  parent.getAttribute('role') === 'row')
              ) {
                folderItem = parent;
                debug(`找到父list-item元素: ${folderItem.outerHTML.substring(0, 100)}`);
                break;
              }
            }
            break;
          }
        }
        
        if (folderElement) break;
      }
    }
    
    // 如果没有找到，尝试使用XPath搜索
    if (!folderElement) {
      debug('使用XPath查找文件夹元素');
      
      const xpath = `//*[contains(text(), "${todayFolderName}") or @title="${todayFolderName}"]`;
      debug(`执行XPath: ${xpath}`);
      
      const result = document.evaluate(
        xpath,
        document,
        null,
        XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
        null
      );
      
      debug(`XPath查找结果: ${result.snapshotLength} 个节点`);
      
      if (result.snapshotLength > 0) {
        folderElement = result.snapshotItem(0);
        debug(`通过XPath找到文件夹元素: ${folderElement.outerHTML.substring(0, 100)}`);
        
        // 向上查找可能的list-item
        let parent = folderElement;
        for (let i = 0; i < 5; i++) {
          if (!parent) break;
          parent = parent.parentElement;
          if (parent && (
              parent.classList.contains('list-item') || 
              parent.classList.contains('file-item') ||
              parent.getAttribute('role') === 'row')
          ) {
            folderItem = parent;
            debug(`找到父list-item元素: ${folderItem.outerHTML.substring(0, 100)}`);
            break;
          }
        }
      }
    }
    
    if (!folderElement) {
      debug(`未找到今日文件夹: ${todayFolderName}`);
      
      // 检查是否需要导航回主页面
      if (document.title.includes('405') || !isFileCloudPage()) {
        debug('检测到页面异常，尝试导航回主页');
        const baseUrl = window.location.href.split('#')[0];
        window.location.href = baseUrl + '#/home/<USER>';
        await sleep(2000);
        return false;
      }
      
      return false;
    }
    
    // 优先使用找到的list-item元素
    const clickableElement = folderItem || folderElement;
    debug(`使用${folderItem ? 'list-item父元素' : '文件夹元素自身'}进行点击操作`);
    
    // 记录找到的元素路径，用于调试
    let elementPath = '';
    let currentElement = clickableElement;
    while (currentElement && currentElement !== document.body) {
      elementPath = `${currentElement.tagName}${currentElement.id ? '#' + currentElement.id : ''}${currentElement.className ? '.' + currentElement.className.replace(/\s+/g, '.') : ''} > ${elementPath}`;
      currentElement = currentElement.parentElement;
      if (elementPath.length > 200) break; // 防止路径过长
    }
    debug(`点击元素路径: ${elementPath}`);
    
    // 获取可能的文件ID
    let fileId = null;
    try {
      fileId = clickableElement.getAttribute('data-id') || 
               clickableElement.getAttribute('file-id') || 
               clickableElement.getAttribute('id');
      
      if (fileId && !isNaN(parseInt(fileId))) {
        debug(`找到文件ID: ${fileId}`);
      }
    } catch (err) {
      debug(`获取文件ID失败: ${err.message}`);
    }
    
    // 直接导航到文件夹，避免使用点击方式
    if (fileId && !isNaN(parseInt(fileId))) {
      try {
        debug(`尝试直接导航到文件夹ID: ${fileId}`);
        const baseUrl = window.location.href.split('?')[0];
        const newUrl = `${baseUrl}?id=${fileId}`;
        debug(`导航到新URL: ${newUrl}`);
        window.location.href = newUrl;
        
        // 等待URL变化
        await sleep(1500);
        return true;
      } catch (err) {
        debug(`直接导航失败: ${err.message}`);
      }
    }
    
    // 如果直接导航失败，尝试点击方式
    // 直接双击元素
    try {
      debug('尝试使用双击事件');
      clickableElement.dispatchEvent(new MouseEvent('dblclick', {
        bubbles: true,
        cancelable: true,
        view: window
      }));
    } catch (err) {
      debug(`双击事件失败: ${err.message}`);
    }
    
    // 等待一小段时间
    await sleep(300);
    
    // 单击备份方案
    clickableElement.click();
    debug('已点击文件夹元素');
    
    // 等待可能出现的上下文菜单
    await sleep(500);
    
    // 查找"打开"或"进入"按钮
    const openButtons = Array.from(document.querySelectorAll('button, [role="menuitem"], .context-menu-item')).filter(
      btn => btn.offsetParent !== null && btn.textContent && (
        btn.textContent.includes('打开') || 
        btn.textContent.includes('进入') || 
        btn.textContent.includes('查看')
      )
    );
    
    if (openButtons.length > 0) {
      debug(`找到"打开"按钮，点击: ${openButtons[0].textContent.trim()}`);
      openButtons[0].click();
      await sleep(1000);
    }
    
    // 等待URL变化或页面重载
    const startUrl = window.location.href;
    let waitCount = 0;
    const maxWait = 10;
    
    while (waitCount < maxWait) {
      await sleep(500);
      waitCount++;
      
      // 检查URL是否变化
      if (window.location.href !== startUrl) {
        debug(`URL已变化: ${window.location.href}`);
        
        // 检查是否进入错误页面
        if (document.title.includes('405')) {
          debug('导航后出现405错误，尝试恢复');
          await handlePageError();
          return false;
        }
        
        // 等待页面内容加载
        await sleep(1000);
        return true;
      }
      
      // 检查面包屑
      const breadcrumbText = document.querySelector('.ant-breadcrumb, .breadcrumb, [class*="breadcrumb"]')?.textContent || '';
      if (breadcrumbText.includes(todayFolderName)) {
        debug(`面包屑导航已更新，包含文件夹名: ${breadcrumbText}`);
        return true;
      }
    }
    
    // 如果没有明确确认，但URL有变化，认为操作可能成功
    if (window.location.href !== startUrl) {
      debug(`URL已变化但无法确认是否进入了正确的文件夹，检查页面状态`);
      
      // 检查是否出现错误
      if (document.title.includes('405')) {
        debug('导航后出现405错误，尝试恢复');
        await handlePageError();
        return false;
      }
      
      return true;
    }
    
    debug('无法确认是否成功进入文件夹，但假定操作成功');
    return true;
  } catch (error) {
    debug(`进入今日文件夹时出错: ${error.message}`);
    
    // 检查是否需要恢复页面
    if (document.title.includes('405')) {
      debug('出现405错误，尝试恢复页面');
      await handlePageError();
    }
    
    return false;
  }
}

// 上传文件 - 重构为无状态版本
async function uploadFile(fileName, fileContent) {
  debug(`开始上传文件: ${fileName}`);
  
  // 移除本地文件跟踪器检查，直接进行上传
  // 服务器端会处理重复上传的检查和状态管理
  
  const fileSize = (() => {
    if (fileContent instanceof Blob || fileContent instanceof File) {
      return fileContent.size;
    }
    if (typeof fileContent === 'string') {
      return fileContent.length;
    }
    if (fileContent instanceof ArrayBuffer) {
      return fileContent.byteLength;
    }
    return 0;
  })();
  
  debug(`准备上传文件: ${fileName} (大小: ${fileSize} 字节)`);
  
  // 通知开始上传状态（通过服务器管理）
  chrome.runtime.sendMessage({
    type: 'UPDATE_FILE_STATUS',
    filename: fileName,
    status: 'uploading'
  });
  
  try {
    // 添加额外调试信息
    debug(`文件内容类型: ${typeof fileContent}, 内容长度: ${typeof fileContent === 'string' ? fileContent.length : (fileContent instanceof Blob ? fileContent.size : '未知')}`);
    
    // 查找上传按钮
    const uploadButton = findUploadButton();
    
    if (!uploadButton) {
      debug('未找到上传按钮');
      return false;
    }
    
    debug(`找到上传按钮: ${uploadButton.textContent.trim()}`);
    
    // 确定文件MIME类型
    let mimeType = 'text/plain';
    if (fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      if (ext === 'jpg' || ext === 'jpeg') mimeType = 'image/jpeg';
      else if (ext === 'png') mimeType = 'image/png';
      else if (ext === 'pdf') mimeType = 'application/pdf';
      else if (ext === 'doc' || ext === 'docx') mimeType = 'application/msword';
      else if (ext === 'xls' || ext === 'xlsx') mimeType = 'application/vnd.ms-excel';
    }
    
    // 创建文件对象，确保正确处理不同类型的fileContent
    let file;
    if (fileContent instanceof Blob || fileContent instanceof File) {
      // 如果已经是Blob或File对象，直接使用
      file = new File([fileContent], fileName, { type: mimeType, lastModified: Date.now() });
    } else if (typeof fileContent === 'string') {
      // 如果是字符串，创建Blob
      const blob = new Blob([fileContent], { type: mimeType });
      file = new File([blob], fileName, { type: mimeType, lastModified: Date.now() });
    } else if (fileContent instanceof ArrayBuffer) {
      // 如果是ArrayBuffer，创建Blob
      const blob = new Blob([fileContent], { type: mimeType });
      file = new File([blob], fileName, { type: mimeType, lastModified: Date.now() });
    } else {
      // 无法处理的类型
      debug('无法处理的文件内容类型');
      return false;
    }
    
    debug(`已创建文件对象: ${fileName}, 大小: ${file.size} 字节, 类型: ${mimeType}`);
    
    // 检查文件大小
    if (file.size === 0) {
      debug('错误: 文件内容为空');
      return false;
    }
    
    // 使用唯一ID标记此次上传，防止重复
    const uploadId = Date.now().toString(36) + Math.random().toString(36).substr(2);
    window['_currentUpload_' + uploadId] = true;
    
    try {
      // 查找文件输入元素
      debug('查找页面中的文件输入元素');
      const uppyInputs = document.querySelectorAll('.uppy-FileInput-input');
      let fileInput = null;
      
      if (uppyInputs.length > 0) {
        fileInput = uppyInputs[0];
        debug(`找到uppy上传组件输入元素: ${fileInput.outerHTML.substring(0, 100)}`);
      } else {
        const fileInputs = Array.from(document.querySelectorAll('input[type="file"]'));
        if (fileInputs.length > 0) {
          fileInput = fileInputs[0];
          debug(`找到标准文件输入元素: ${fileInput.outerHTML.substring(0, 100)}`);
        }
      }
      
      // 如果未找到输入元素，尝试点击上传按钮查看是否会创建一个
      if (!fileInput) {
        debug('未找到文件输入元素，尝试点击上传按钮');
        uploadButton.click();
        await sleep(500);
        
        // 再次查找
        const newInputs = document.querySelectorAll('.uppy-FileInput-input, input[type="file"]');
        if (newInputs.length > 0) {
          fileInput = newInputs[0];
          debug(`点击后找到文件输入元素: ${fileInput.outerHTML.substring(0, 100)}`);
        } else {
          debug('点击后仍未找到文件输入元素');
          return false;
        }
      }
      
      // 保存原始处理程序
      const originalChange = fileInput.onchange;
      const originalClick = fileInput.onclick;
      
      try {
        // 创建一个标记来防止重复触发事件
        let changeTriggered = false;
        
        // 替换处理程序以阻止文件选择对话框
        fileInput.onclick = function(e) {
          if (window['_currentUpload_' + uploadId]) {
            debug('阻止文件选择对话框打开');
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        };
        
        // 替换change处理程序以确保只触发一次
        fileInput.onchange = function(e) {
          if (changeTriggered) {
            debug('阻止重复的change事件');
            return;
          }
          changeTriggered = true;
          // 调用原始处理程序
          if (originalChange) {
            originalChange.call(this, e);
          }
        };
        
        // 点击上传按钮
        debug('点击上传按钮（已阻止文件选择对话框）');
        uploadButton.click();
        await sleep(300);
        
        // 创建DataTransfer对象
        debug('准备设置文件到输入元素');
        const dataTransfer = new DataTransfer();
        
        // 添加文件到dataTransfer
        dataTransfer.items.add(file);
        debug(`DataTransfer中的文件数量: ${dataTransfer.files.length}`);
        
        // 设置文件到输入元素
        fileInput.files = dataTransfer.files;
        debug('成功设置文件到输入元素');
        
        // 创建并触发单个change事件
        const changeEvent = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(changeEvent);
        debug('已触发change事件');
        
        // 额外监视文件上传状态
        // 这段代码会监控在uppy上传组件中是否有重复的文件添加
        const checkForDuplicates = () => {
          // 查找uppy的文件列表容器
          const fileListContainers = document.querySelectorAll('.uppy-Dashboard-files, .uppy-ProviderBrowser-list');
          if (fileListContainers.length > 0) {
            debug('检查上传队列中的文件');
            
            // 获取所有文件条目
            const fileItems = document.querySelectorAll('.uppy-Dashboard-Item, .uppy-ProviderBrowserItem');
            debug(`上传队列中有 ${fileItems.length} 个文件`);
            
            // 如果发现多个相同名称的文件，尝试删除重复项
            const fileNames = {};
            for (const item of fileItems) {
              const nameEl = item.querySelector('.uppy-Dashboard-Item-name, .uppy-ProviderBrowserItem-name');
              if (nameEl) {
                const name = nameEl.textContent.trim();
                if (fileNames[name]) {
                  // 找到删除按钮并点击它
                  const removeBtn = item.querySelector('button[data-uppy-remove], .uppy-Dashboard-Item-remove');
                  if (removeBtn) {
                    debug(`发现并删除重复文件: ${name}`);
                    removeBtn.click();
                  }
                } else {
                  fileNames[name] = true;
                }
              }
            }
          }
        };
        
        // 在设置文件后执行检查
        setTimeout(checkForDuplicates, 500);
        
        // 等待上传开始
        debug('等待上传开始');
        await sleep(1000);
        
        // 检查是否有上传进度指示器
        const progressIndicator = document.querySelector('.progress, .progress-bar, [class*="upload-progress"]');
        if (progressIndicator) {
          debug('检测到上传进度指示器，等待上传完成');
          await sleep(5000);
        } else {
          debug('未检测到标准进度指示器，等待固定时间');
          await sleep(3000);
        }
        
        // 检查是否上传成功
        const successIndicator = document.querySelector('.upload-success, .success-message, [class*="success"]');
        const errorIndicator = document.querySelector('.upload-error, .error-message, [class*="error"]');
        
        if (errorIndicator) {
          debug('检测到错误指示器，上传可能失败');
          return false;
        }
        
        if (successIndicator) {
          debug('检测到成功指示器，上传完成');
        } else {
          debug('未检测到标准成功指示器，但假定上传成功');
        }
        
        // 成功完成上传，保持文件在跟踪列表中
        return true;
      } finally {
        // 恢复原始处理程序
        if (fileInput) {
          fileInput.onclick = originalClick;
          fileInput.onchange = originalChange;
        }
        
        // 清除临时标记
        delete window['_currentUpload_' + uploadId];
      }
    } catch (error) {
      debug(`上传过程中发生错误: ${error.message}`);
      // 上传失败，从跟踪列表中移除
      return false;
    } finally {
      // 确保清除临时标记
      delete window['_currentUpload_' + uploadId];
    }
  } catch (error) {
    debug(`上传文件时出错: ${error.message}`);
    // 出现错误，从跟踪列表中移除文件
    return false;
  }
}

// 使用XPath查找元素的辅助函数
async function findElementByXPath(xpath, maxRetries = 5, retryInterval = 300) {
  debug(`通过XPath查找元素: ${xpath}`);
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = document.evaluate(
        xpath, 
        document, 
        null, 
        XPathResult.FIRST_ORDERED_NODE_TYPE, 
        null
      );
      
      const element = result.singleNodeValue;
      
      if (element) {
        debug(`找到元素: ${xpath}`);
        return element;
      }
      
      // 如果没找到并且还有重试次数，等待后重试
      if (i < maxRetries - 1) {
        debug(`未找到元素，等待后重试: ${xpath}`);
        await sleep(retryInterval);
      }
    } catch (error) {
      debug(`XPath查询出错: ${error.message}`);
      if (i < maxRetries - 1) {
        await sleep(retryInterval);
      }
    }
  }
  
  debug(`最终未找到元素: ${xpath}`);
  return null;
}

// 等待元素消失
async function waitForElementToDisappear(selector, timeoutMs) {
  debug(`等待元素消失: ${selector}`);
  
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeoutMs) {
    const element = document.querySelector(selector);
    if (!element) {
      debug(`元素已消失: ${selector}`);
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  debug(`等待元素消失超时: ${selector}`);
  throw new Error(`等待元素消失超时: ${selector}`);
}

// 通知用户
function notifyUser(message, type = 'info') {
  debug(`通知用户: ${message} (${type})`);
  
  // 寻找应用中的通知组件
  const notifier = window.notifier || 
                   window.$notify || 
                   window.$message || 
                   window.Notification;
  
  if (notifier) {
    try {
      if (typeof notifier === 'function') {
        notifier({ message, type });
      } else if (notifier[type]) {
        notifier[type](message);
      }
      return;
    } catch (error) {
      debug(`使用应用通知组件失败: ${error.message}`);
    }
  }
  
  // 使用原生alert作为备选
  alert(message);
}

// 处理文件上传流程
async function processFileUpload(file) {
  debug(`开始处理文件上传: ${file.name}`);
  
  try {
    // 检查是否在文件云页面
    if (!await isFileCloudPage()) {
      debug('当前不是文件云页面，无法上传文件');
      return { success: false, message: '当前不是文件云页面' };
    }
    
    // 等待应用程序加载完成
    debug('等待应用程序加载完成');
    if (!await waitForAppReady()) {
      debug('应用程序加载超时');
      return { success: false, message: '应用程序加载超时' };
    }
    
    // 检查今日文件夹是否存在
    debug('检查今日文件夹是否存在');
    const todayFolderExists = await checkTodayFolderExists();
    
    // 如果今日文件夹不存在，则创建
    if (!todayFolderExists) {
      debug('今日文件夹不存在，开始创建');
      const created = await createTodayFolder();
      if (!created) {
        debug('创建今日文件夹失败');
        return { success: false, message: '创建今日文件夹失败' };
      }
      debug('今日文件夹创建成功');
    } else {
      debug('今日文件夹已存在');
    }
    
    // 进入今日文件夹
    debug('开始进入今日文件夹');
    const entered = await enterTodayFolder();
    if (!entered) {
      debug('进入今日文件夹失败');
      return { success: false, message: '进入今日文件夹失败' };
    }
    debug('已成功进入今日文件夹');
    
    // 上传文件
    debug('开始上传文件');
    const uploaded = await uploadFile(file.name, file);
    if (!uploaded) {
      debug('文件上传失败');
      return { success: false, message: '文件上传失败' };
    }
    
    debug('文件上传成功');
    return { success: true, message: '文件上传成功' };
  } catch (error) {
    debug(`处理文件上传过程中出错: ${error.message}`);
    return { success: false, message: `上传过程中出错: ${error.message}` };
  }
}

// 初始化文件上传处理程序
function initFileUploadHandler() {
  debug('初始化文件上传处理程序');
  
  // 移除重复的消息监听器 - 这个功能已经在initPage中处理了
  // chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
  //   debug(`收到消息: ${JSON.stringify(request)}`);
  //
  //   // 检查是否在云盘页面，如果是则不处理，让云盘专用脚本处理
  //   if (window.location.href.includes('yunpan.gdcourts.gov.cn')) {
  //     debug('在云盘页面，跳过content.js处理，让云盘专用脚本处理');
  //     return false; // 不处理此消息
  //   }
  //
  //   // 立即发送响应，表明我们会处理这个请求
  //   sendResponse({ status: 'processing' });
  //
  //   // 防止重复处理消息
  //   // 避免与handleStartUpload函数重复处理
  //   if (request.type === 'START_UPLOAD_PROCESS' && !window._uploadInProgress) {
  //     debug('收到开始上传处理的消息');
  //
  //     // 直接调用统一的处理函数，并传递标记以避免重复处理
  //     await handleStartUpload({action: 'START_UPLOAD_PROCESS', fromMessageListener: true});
  //   }
  // });
  
  debug('文件上传处理程序已初始化');
}

// 从background获取文件数据的函数
async function getFileDataFromBackground(filename) {
  debug(`从background请求文件数据: ${filename}`);
  
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ 
      type: 'GET_FILE_DATA', 
      filename: filename 
    }, (response) => {
      if (response && response.data) {
        // 如果返回的是base64字符串，转换为Blob
        if (response.data.base64) {
          const base64Data = response.data.base64;
          const byteString = atob(base64Data.split(',')[1] || base64Data);
          const mimeType = (base64Data.split(',')[0] || '').match(/:(.*?);/) ? 
                           (base64Data.split(',')[0] || '').match(/:(.*?);/)[1] : 
                           'application/octet-stream';
          
          const ab = new ArrayBuffer(byteString.length);
          const ia = new Uint8Array(ab);
          
          for (let i = 0; i < byteString.length; i++) {
            ia[i] = byteString.charCodeAt(i);
          }
          
          const blob = new Blob([ab], { type: mimeType });
          resolve({ blob, type: mimeType });
        } 
        // 如果已经是ArrayBuffer，转换为Blob
        else if (response.data.buffer) {
          const buffer = response.data.buffer;
          const mimeType = response.data.type || 'application/octet-stream';
          const blob = new Blob([buffer], { type: mimeType });
          resolve({ blob, type: mimeType });
        }
        // 如果直接返回了blob URL
        else if (response.data.blobUrl) {
          fetch(response.data.blobUrl)
            .then(response => response.blob())
            .then(blob => {
              resolve({ blob, type: blob.type });
            })
            .catch(error => {
              debug(`获取blob URL内容失败: ${error.message}`);
              resolve(null);
            });
        }
        else {
          debug('收到无法识别的文件数据格式');
          resolve(null);
        }
      } else {
        debug('未收到文件数据或数据格式错误');
        resolve(null);
      }
    });
  });
}

// 直接执行初始化
debug('content.js 加载完成，准备初始化');
initFileUploadHandler();

// 延迟检测页面元素
setTimeout(async () => {
  debug('开始页面元素检测');
  
  try {
    // 检查是否是文件云页面
    const isFilePage = await isFileCloudPage();
    debug(`是否是文件云页面: ${isFilePage}`);
    
    if (!isFilePage) {
      debug('当前不是文件云页面，跳过元素检测');
      return;
    }
    
    // 检查上传按钮
    const uploadButtonSelectors = [
      '[data-action="upload"]',
      '.upload-btn',
      'button[title*="上传"]',
      'button[aria-label*="上传"]',
      '.file-upload',
      '#uploadButton'
    ];
    
    debug('检查上传按钮选择器:');
    for (const selector of uploadButtonSelectors) {
      const elements = document.querySelectorAll(selector);
      debug(`- 选择器 ${selector}: 找到 ${elements.length} 个元素`);
      
      if (elements.length > 0) {
        for (let i = 0; i < Math.min(elements.length, 3); i++) {
          const el = elements[i];
          const tagName = el.tagName;
          const className = el.className;
          const text = el.textContent.trim();
          debug(`  元素 ${i+1}: ${tagName}, class="${className}", text="${text}"`);
        }
      }
    }
    
    // 检查新建文件夹按钮
    const folderButtonSelectors = [
      '[data-action="new-folder"]', 
      '.create-folder-btn',
      '.new-folder-btn',
      'button[title*="新建文件夹"]',
      'button[aria-label*="新建文件夹"]',
      '.add-folder',
      '#createFolder'
    ];
    
    debug('检查新建文件夹按钮选择器:');
    for (const selector of folderButtonSelectors) {
      const elements = document.querySelectorAll(selector);
      debug(`- 选择器 ${selector}: 找到 ${elements.length} 个元素`);
      
      if (elements.length > 0) {
        for (let i = 0; i < Math.min(elements.length, 3); i++) {
          const el = elements[i];
          const tagName = el.tagName;
          const className = el.className;
          const text = el.textContent.trim();
          debug(`  元素 ${i+1}: ${tagName}, class="${className}", text="${text}"`);
        }
      }
    }
    
    // 检查通用按钮
    debug('检查页面上的按钮元素:');
    const allButtons = document.querySelectorAll('button, [role="button"], .btn');
    debug(`- 找到 ${allButtons.length} 个按钮元素`);
    
    if (allButtons.length > 0) {
      const buttonTexts = Array.from(allButtons)
        .map(btn => btn.textContent.trim())
        .filter(text => text.length > 0);
      
      debug(`- 按钮文本: ${buttonTexts.join(', ')}`);
    }
    
    // 检查文件列表容器
    const fileListSelectors = [
      '.file-list', 
      '.folder-list', 
      '[data-role="file-list"]'
    ];
    
    debug('检查文件列表容器选择器:');
    for (const selector of fileListSelectors) {
      const elements = document.querySelectorAll(selector);
      debug(`- 选择器 ${selector}: 找到 ${elements.length} 个元素`);
      
      if (elements.length > 0) {
        for (let i = 0; i < Math.min(elements.length, 3); i++) {
          const el = elements[i];
          const childCount = el.children.length;
          debug(`  元素 ${i+1}: 子元素数量 ${childCount}`);
        }
      }
    }
    
    // 测试进入今日文件夹
    debug('尝试检查今日文件夹是否存在:');
    const folderExists = await checkTodayFolderExists();
    debug(`- 今日文件夹存在: ${folderExists}`);
    
    // 找出页面中的特殊元素和类名
    debug('分析页面特殊元素:');
    const classes = new Set();
    const allElements = document.querySelectorAll('*');
    
    // 收集类名
    allElements.forEach(el => {
      if (el.className && typeof el.className === 'string') {
        el.className.split(' ').forEach(cls => {
          if (cls && (cls.includes('upload') || cls.includes('file') || cls.includes('folder'))) {
            classes.add(cls);
          }
        });
      }
    });
    
    debug(`- 找到相关类名: ${Array.from(classes).join(', ')}`);
    
    // 捕获第一个"上传"相关按钮的属性
    const uploadBtnText = document.evaluate(
      "//*[contains(text(), '上传')]",
      document,
      null,
      XPathResult.FIRST_ORDERED_NODE_TYPE,
      null
    ).singleNodeValue;
    
    if (uploadBtnText) {
      const btn = uploadBtnText.closest('button') || uploadBtnText;
      debug('- 找到上传按钮:');
      debug(`  标签: ${btn.tagName}`);
      debug(`  类名: ${btn.className}`);
      debug(`  ID: ${btn.id}`);
      debug(`  文本: ${btn.textContent.trim()}`);
      
      // 获取所有属性
      const attrs = {};
      Array.from(btn.attributes).forEach(attr => {
        attrs[attr.name] = attr.value;
      });
      debug(`  属性: ${JSON.stringify(attrs)}`);
    } else {
      debug('- 未找到上传按钮');
    }
    
    debug('页面元素检测完成');
  } catch (error) {
    debug(`页面元素检测出错: ${error.message}`);
  }
}, 3000);

// 导出函数用于测试
window.fileUploadUtils = {
  processFileUpload,
  checkTodayFolderExists,
  createTodayFolder,
  enterTodayFolder,
  uploadFile,
  debug
};

// 导出
window.ylzFileCloudUploader = {
  checkPage: isFileCloudPage,
  processFiles: processFilesQueue,
  debugMode: {
    enable: () => { isDebugMode = true; debug('调试模式已启用'); },
    disable: () => { isDebugMode = false; debug('调试模式已禁用'); },
    getState: () => isDebugMode
  }
};

debug('文件云上传助手初始化完成');

// 获取真实待上传文件列表
async function getFilesFromPopup() {
  debug('从popup获取待上传文件列表');
  
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: 'GET_PENDING_FILES' }, (response) => {
      if (response && response.files && Array.isArray(response.files)) {
        debug(`获取到 ${response.files.length} 个待上传文件`);
        resolve(response.files);
      } else {
        debug('未获取到待上传文件，或返回格式错误');
        resolve([]);
      }
    });
  });
}

// 导出
window.ylzFileCloudUploader = {
  checkPage: isFileCloudPage,
  processFiles: processFilesQueue,
  debugMode: {
    enable: () => { isDebugMode = true; debug('调试模式已启用'); },
    disable: () => { isDebugMode = false; debug('调试模式已禁用'); },
    getState: () => isDebugMode
  }
};

debug('文件云上传助手初始化完成');

// 处理上传启动
async function handleStartUpload(request) {
  try {
    debug('开始处理上传请求');
    
    // 添加单次执行标志，防止重复处理
    if (window._uploadInProgress) {
      debug('上传已在进行中，忽略重复请求');
      chrome.runtime.sendMessage({
        action: 'UPLOAD_COMPLETED',
        success: false,
        message: '上传已在进行中，请等待当前上传完成'
      });
      return;
    }
    
    // 标记上传正在进行中
    window._uploadInProgress = true;
    
    // 详细记录请求内容，帮助调试
    debug(`请求内容: action=${request?.action}`);
    
    try {
      // 保存当前页面状态用于调试
      await recordPageState();
      
      // 检查当前是否在文件云页面
      const onFileCloudPage = await isFileCloudPage();
      if (!onFileCloudPage) {
        debug('当前不在文件云页面，无法上传文件');
        chrome.runtime.sendMessage({
          action: 'UPLOAD_COMPLETED',
          success: false,
          message: '当前不在文件云页面，无法上传文件'
        });
        window._uploadInProgress = false;
        return;
      }
      
      // 检查今天的文件夹是否存在，如果不存在则创建
      const todayFolderExists = await checkTodayFolderExists();
      if (!todayFolderExists) {
        debug('今天的文件夹不存在，开始创建');
        try {
          await createTodayFolder();
        } catch (error) {
          debug('创建今天的文件夹失败: ' + error.message);
          chrome.runtime.sendMessage({
            action: 'UPLOAD_COMPLETED',
            success: false,
            message: '创建今天的文件夹失败: ' + error.message
          });
          window._uploadInProgress = false;
          return;
        }
      }
      
      // 进入今天的文件夹
      try {
        await enterTodayFolder();
      } catch (error) {
        debug('进入今天的文件夹失败: ' + error.message);
        chrome.runtime.sendMessage({
          action: 'UPLOAD_COMPLETED',
          success: false,
          message: '进入今天的文件夹失败: ' + error.message
        });
        window._uploadInProgress = false;
        return;
      }

      // 获取真实待上传文件列表
      const pendingFiles = await getFilesFromPopup();
      
      if (pendingFiles.length === 0) {
        debug('没有待上传的文件');
        chrome.runtime.sendMessage({
          action: 'UPLOAD_COMPLETED',
          success: false,
          message: '没有待上传的文件'
        });
        window._uploadInProgress = false;
        return;
      }
      
      // 构建更有效的文件跟踪器，避免重复上传
      // 通过文件名和大小建立更可靠的文件标识
      const fileSignatures = new Map();
      const duplicateFiles = [];
      
      // 预处理文件列表，检测潜在的重复文件
      pendingFiles.forEach(file => {
        const fileBaseName = file.filename.split('.')[0];
        if (fileSignatures.has(fileBaseName)) {
          // 发现可能的重复文件
          duplicateFiles.push({
            original: fileSignatures.get(fileBaseName),
            duplicate: file
          });
        } else {
          fileSignatures.set(fileBaseName, file);
        }
      });
      
      if (duplicateFiles.length > 0) {
        debug(`检测到 ${duplicateFiles.length} 个可能的重复文件:`);
        duplicateFiles.forEach(dup => {
          debug(`- 原始文件: ${dup.original.filename}, 重复文件: ${dup.duplicate.filename}`);
        });
      }
      
      // 追踪去重处理的文件，避免重复上传相同文件
      const processedFiles = new Set();
      
      // 记录所有成功上传的文件
      const uploadedFiles = [];
      
      // 遍历文件列表逐个上传
      for (let i = 0; i < pendingFiles.length; i++) {
        const file = pendingFiles[i];
        
        // 检查文件是否已经处理过，防止重复上传
        // 更强的重复检查 - 同时检查文件名的基础部分
        const fileBaseName = file.filename.split('.')[0];
        
        if (processedFiles.has(file.filename) || processedFiles.has(fileBaseName)) {
          debug(`文件 ${file.filename} 或其基础名 ${fileBaseName} 已经处理过，跳过`);
          continue;
        }
        
        // 标记文件为已处理 - 同时记录基础名，防止上传同名不同扩展名的文件
        processedFiles.add(file.filename);
        processedFiles.add(fileBaseName);
        
        if (!file.status || file.status === 'pending') {
          const fileName = file.originalname || file.filename;
          
          debug(`准备上传列表中的第 ${i+1}/${pendingFiles.length} 个文件: ${fileName}`);
          
          try {
            // 获取文件数据
            const fileData = await getFileDataFromBackground(file.filename);
            
            if (!fileData || !fileData.blob) {
              throw new Error(`无法获取文件 ${fileName} 的数据`);
            }
            
            debug(`成功获取文件数据，大小: ${fileData.blob.size} 字节`);
            
            // 创建File对象
            const fileObj = new File([fileData.blob], fileName, { 
              type: file.mimetype || fileData.blob.type || 'application/octet-stream',
              lastModified: new Date().getTime()
            });
            
            // 上传单个文件 - 直接传递File对象
            const success = await uploadFile(fileName, fileObj);
            
            if (success) {
              uploadedFiles.push(fileName);
              debug(`文件 ${fileName} 上传成功`);
              
              // 更新文件状态 - 同时更新任何同名基础的文件
              const filesWithSameBase = pendingFiles.filter(f => 
                f.filename.split('.')[0] === fileBaseName
              );
              
              if (filesWithSameBase.length > 1) {
                debug(`找到 ${filesWithSameBase.length} 个基础名相同的文件，全部标记为已上传`);
                
                filesWithSameBase.forEach(sameBaseFile => {
                  chrome.runtime.sendMessage({
                    type: 'UPDATE_FILE_STATUS',
                    filename: sameBaseFile.filename,
                    status: 'uploaded'
                  });
                });
              } else {
                // 正常更新单个文件
                chrome.runtime.sendMessage({
                  type: 'UPDATE_FILE_STATUS',
                  filename: file.filename,
                  status: 'uploaded'
                });
              }
            } else {
              debug(`文件 ${fileName} 上传失败`);
              
              // 更新文件状态
              chrome.runtime.sendMessage({
                type: 'UPDATE_FILE_STATUS',
                filename: file.filename,
                status: 'error'
              });
            }
            
            // 每个文件上传间隔一段时间，避免冲突
            if (i < pendingFiles.length - 1) {
              await sleep(2000);
            }
          } catch (fileError) {
            debug(`上传文件 ${fileName} 时出错: ${fileError.message}`);
            
            // 更新文件状态
            chrome.runtime.sendMessage({
              type: 'UPDATE_FILE_STATUS',
              filename: file.filename,
              status: 'error'
            });
          }
        }
      }
      
      // 发送完成消息
      if (uploadedFiles.length > 0) {
        debug(`成功上传了 ${uploadedFiles.length}/${pendingFiles.length} 个文件`);
        chrome.runtime.sendMessage({
          action: 'UPLOAD_COMPLETED',
          success: true,
          message: `成功上传了 ${uploadedFiles.length}/${pendingFiles.length} 个文件`,
          fileNames: uploadedFiles
        });
        
        // 同步状态到服务器，确保每个上传的文件都被正确标记
        uploadedFiles.forEach(fileName => {
          debug(`发送状态更新到服务器: ${fileName} -> uploaded`);
          chrome.runtime.sendMessage({
            type: 'SEND_WS_MESSAGE',
            wsMessage: {
              type: 'update_file_status',
              filename: fileName,
              status: 'uploaded'
            }
          });
        });
      } else {
        debug('所有文件上传失败');
        chrome.runtime.sendMessage({
          action: 'UPLOAD_COMPLETED',
          success: false,
          message: '所有文件上传失败'
        });
      }
      
    } finally {
      // 完成后清除标志
      window._uploadInProgress = false;
    }
  } catch (error) {
    debug(`处理上传请求时出错: ${error.message}`);
    window._uploadInProgress = false;
    chrome.runtime.sendMessage({
      action: 'UPLOAD_COMPLETED',
      success: false,
      message: `处理上传请求时出错: ${error.message}`
    });
  }
}

// 移除重复的消息监听器 - 这个功能已经在initPage中处理了
// chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
//   debug('收到消息: ' + JSON.stringify(request));
//
//   if (request.action === 'CHECK_PAGE') {
//     isFileCloudPage().then(result => {
//       sendResponse({isFileCloudPage: result});
//     });
//     return true; // 保持消息通道开放以进行异步响应
//   }
  
  if (request.action === 'START_UPLOAD_PROCESS') {
    debug('收到上传请求，数据：' + (request.fileName ? ('文件名：' + request.fileName) : '无文件名') + 
          (request.fileData ? '，包含文件数据' : '，无文件数据') +
          (request.fileList ? `，包含${request.fileList.length}个文件` : '')
    );
    
    // 错误检查：确保有文件数据
    if (!request.fileData && (!request.fileList || !Array.isArray(request.fileList) || request.fileList.length === 0)) {
      debug('请求中没有提供文件数据');
      chrome.runtime.sendMessage({
        action: 'UPLOAD_COMPLETED',
        success: false,
        message: '没有提供文件数据'
      });
      sendResponse({received: false, error: '没有提供文件数据'});
      return true;
    }
    
    // 启动上传过程
    handleStartUpload(request);
    sendResponse({received: true});
    return true;
  }
  
  return false;
});

// 将调试日志功能暴露给控制台，方便调试
window.toggleDebug = function(enable) {
  isDebugMode = enable !== undefined ? enable : !isDebugMode;
  debug(`调试日志已${isDebugMode ? '启用' : '禁用'}`);
  return `调试日志已${isDebugMode ? '启用' : '禁用'}`;
}; 

// 处理页面错误和恢复
async function handlePageError() {
  debug('检测到页面错误，尝试恢复');
  
  // 检查是否为405错误
  if (document.title.includes('405') || document.body.textContent.includes('405 Not Allowed')) {
    debug('检测到405错误，尝试恢复页面');
    
    // 方法1：尝试回到根路径
    try {
      const baseUrl = window.location.href.split('#')[0];
      const homeUrl = baseUrl + '#/home/<USER>';
      debug(`尝试导航回主页: ${homeUrl}`);
      window.location.href = homeUrl;
      return true;
    } catch (error) {
      debug(`导航到主页失败: ${error.message}`);
    }
    
    // 方法2：尝试修改URL中的ID参数
    try {
      const currentUrl = window.location.href;
      if (currentUrl.includes('id=')) {
        const newUrl = currentUrl.replace(/id=\d+/, 'id=0');
        debug(`尝试修改URL ID参数: ${newUrl}`);
        window.location.href = newUrl;
        return true;
      }
    } catch (error) {
      debug(`修改URL参数失败: ${error.message}`);
    }
    
    // 方法3：使用历史API回退
    try {
      debug('尝试使用历史API回退');
      window.history.back();
      return true;
    } catch (error) {
      debug(`历史回退失败: ${error.message}`);
    }
    
    debug('所有自动恢复方法失败');
    return false;
  }
  
  debug('页面状态正常或非405错误');
  return false;
}

// 获取真实待上传文件列表
async function getFilesFromPopup() {
  debug('从popup获取待上传文件列表');
  
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({ type: 'GET_PENDING_FILES' }, (response) => {
      if (response && response.files && Array.isArray(response.files)) {
        debug(`获取到 ${response.files.length} 个待上传文件`);
        resolve(response.files);
      } else {
        debug('未获取到待上传文件，或返回格式错误');
        resolve([]);
      }
    });
  });
}

// 移除重复的消息监听器 - 这个功能已经在其他地方处理了
// chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
//   // 移除本地缓存清理逻辑，由服务器管理状态
//   // 移除: if (message.type === 'CLEAR_UPLOAD_TRACKER')
//   // 移除: if (message.type === 'SYNC_UPLOAD_STATE')
//
//   // 保留必要的消息处理
//   if (message.type === 'START_UPLOAD_PROCESS') {
//     handleStartUpload(message);
//     sendResponse({ success: true });
//   }
//
//   return true; // 保持异步响应
// });
