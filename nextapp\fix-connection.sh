#!/bin/bash

# 文件云流转应用连接修复脚本

echo "=== 文件云流转应用连接修复 ==="
echo "时间: $(date)"
echo ""

# 1. 停止现有服务
echo "1. 停止现有服务..."
echo "-------------------"

if [ -f "stop-background.sh" ]; then
    ./stop-background.sh
    sleep 2
else
    echo "未找到停止脚本，手动清理进程..."
    
    if [ -f "app.pid" ]; then
        PIDS=$(cat app.pid)
        for PID in $PIDS; do
            if ps -p $PID > /dev/null 2>&1; then
                echo "终止进程 $PID"
                kill $PID
            fi
        done
        rm -f app.pid
    fi
    
    # 清理可能的僵尸进程
    pkill -f "node server.js"
    pkill -f "node websocket-server.js"
fi

echo ""

# 2. 检查端口占用
echo "2. 检查并清理端口占用..."
echo "-------------------"

MAIN_PORT=3000
WS_PORT=6656

for PORT in $MAIN_PORT $WS_PORT; do
    PID=$(netstat -tuln 2>/dev/null | grep ":$PORT " | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$PID" ] && [ "$PID" != "-" ]; then
        echo "端口 $PORT 被进程 $PID 占用，尝试终止..."
        kill $PID 2>/dev/null || echo "无法终止进程 $PID"
    else
        echo "端口 $PORT 空闲"
    fi
done

echo ""

# 3. 检查必要文件
echo "3. 检查必要文件..."
echo "-------------------"

REQUIRED_FILES=("server.js" "websocket-server.js" "package.json")
MISSING_FILES=()

for FILE in "${REQUIRED_FILES[@]}"; do
    if [ -f "$FILE" ]; then
        echo "✅ $FILE 存在"
    else
        echo "❌ $FILE 不存在"
        MISSING_FILES+=("$FILE")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo ""
    echo "❌ 缺少必要文件，无法继续修复:"
    for FILE in "${MISSING_FILES[@]}"; do
        echo "   - $FILE"
    done
    exit 1
fi

echo ""

# 4. 检查依赖
echo "4. 检查Node.js依赖..."
echo "-------------------"

if [ -f "package.json" ]; then
    if [ ! -d "node_modules" ]; then
        echo "安装依赖..."
        npm install
    else
        echo "✅ node_modules 存在"
    fi
else
    echo "❌ package.json 不存在"
    exit 1
fi

echo ""

# 5. 创建必要目录
echo "5. 创建必要目录..."
echo "-------------------"

REQUIRED_DIRS=("logs" "data" "data/file-status" "public/uploads" "uploads/portals")

for DIR in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$DIR" ]; then
        echo "创建目录: $DIR"
        mkdir -p "$DIR"
    else
        echo "✅ $DIR 存在"
    fi
done

echo ""

# 6. 修复API端点
echo "6. 检查API端点..."
echo "-------------------"

API_FILE="pages/api/admin/files/status.js"
if [ -f "$API_FILE" ]; then
    echo "✅ API端点文件存在: $API_FILE"
else
    echo "❌ API端点文件不存在: $API_FILE"
    echo "请确保Next.js项目结构正确"
fi

echo ""

# 7. 重新启动服务
echo "7. 重新启动服务..."
echo "-------------------"

if [ -f "start-background.sh" ]; then
    echo "使用启动脚本..."
    chmod +x start-background.sh
    ./start-background.sh
else
    echo "手动启动服务..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动主服务器
    echo "启动主服务器..."
    NODE_ENV=production nohup node server.js > logs/server.log 2>&1 &
    SERVER_PID=$!
    echo $SERVER_PID > server.pid
    
    # 启动WebSocket服务器
    echo "启动WebSocket服务器..."
    nohup node websocket-server.js > logs/websocket.log 2>&1 &
    WS_PID=$!
    echo $WS_PID > websocket.pid
    
    # 保存PID
    echo "$SERVER_PID $WS_PID" > app.pid
    
    echo "服务已启动:"
    echo "  主服务器 PID: $SERVER_PID"
    echo "  WebSocket PID: $WS_PID"
fi

echo ""

# 8. 等待服务启动
echo "8. 等待服务启动..."
echo "-------------------"

sleep 5

# 9. 验证修复结果
echo "9. 验证修复结果..."
echo "-------------------"

# 检查进程
if [ -f "app.pid" ]; then
    PIDS=$(cat app.pid)
    ALL_RUNNING=true
    
    for PID in $PIDS; do
        if ps -p $PID > /dev/null 2>&1; then
            echo "✅ 进程 $PID 正在运行"
        else
            echo "❌ 进程 $PID 未运行"
            ALL_RUNNING=false
        fi
    done
    
    if [ "$ALL_RUNNING" = true ]; then
        echo "✅ 所有服务进程正常运行"
    else
        echo "❌ 部分服务进程未运行，请检查日志"
    fi
else
    echo "❌ 未找到PID文件"
fi

# 检查端口
MAIN_PORT=3000
WS_PORT=6656

if netstat -tuln 2>/dev/null | grep ":$MAIN_PORT " > /dev/null; then
    echo "✅ 主服务器端口 $MAIN_PORT 正常监听"
else
    echo "❌ 主服务器端口 $MAIN_PORT 未监听"
fi

if netstat -tuln 2>/dev/null | grep ":$WS_PORT " > /dev/null; then
    echo "✅ WebSocket端口 $WS_PORT 正常监听"
else
    echo "❌ WebSocket端口 $WS_PORT 未监听"
fi

echo ""

# 10. 提供后续建议
echo "10. 后续建议:"
echo "-------------------"

echo "如果修复成功，请:"
echo "  1. 在浏览器中访问: http://localhost:$MAIN_PORT"
echo "  2. 检查文件管理页面: http://localhost:$MAIN_PORT/files"
echo "  3. 查看实时日志: tail -f logs/server.log logs/websocket.log"
echo ""

echo "如果仍有问题，请:"
echo "  1. 运行诊断脚本: ./diagnose-connection.sh"
echo "  2. 查看详细日志: cat logs/server.log logs/websocket.log"
echo "  3. 检查防火墙设置"
echo ""

echo "=== 修复完成 ==="
