@echo off
echo ========================================
echo 文件云流转系统 - Windows启动脚本
echo ========================================

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js版本:
node --version

echo npm版本:
npm --version

echo.

REM 创建必要目录
echo 创建必要目录...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "data\file-status" mkdir data\file-status
if not exist "public\uploads" mkdir public\uploads
if not exist "uploads\portals" mkdir uploads\portals

echo.

REM 检查依赖
echo 检查依赖...
if not exist "node_modules" (
    echo 安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo 依赖已存在
)

echo.

REM 清理缓存
echo 清理缓存...
if exist ".next" (
    rmdir /s /q .next
    echo 已清理.next目录
)

npm cache clean --force >nul 2>&1
echo 已清理npm缓存

echo.

REM 构建项目
echo 构建项目...
npm run build
if %errorlevel% neq 0 (
    echo 警告: 构建失败，尝试开发模式启动
    goto dev_mode
)

echo 构建成功！

echo.

REM 启动服务
echo 启动服务...
echo 主服务器将在端口3000启动
echo WebSocket服务器将在端口6656启动
echo.

REM 启动主服务器
echo 启动主服务器...
start /b cmd /c "node server.js > logs\server.log 2>&1"

REM 等待2秒
timeout /t 2 /nobreak >nul

REM 启动WebSocket服务器
echo 启动WebSocket服务器...
start /b cmd /c "node websocket-server.js > logs\websocket.log 2>&1"

REM 等待3秒让服务启动
echo 等待服务启动...
timeout /t 3 /nobreak >nul

goto check_services

:dev_mode
echo.
echo ========================================
echo 使用开发模式启动
echo ========================================
echo.

REM 开发模式启动
echo 启动开发服务器...
start /b cmd /c "npm run dev > logs\dev.log 2>&1"

REM 启动WebSocket服务器
timeout /t 3 /nobreak >nul
echo 启动WebSocket服务器...
start /b cmd /c "node websocket-server.js > logs\websocket.log 2>&1"

timeout /t 5 /nobreak >nul

:check_services
echo.
echo ========================================
echo 检查服务状态
echo ========================================

REM 检查端口占用
echo 检查端口占用...
netstat -an | findstr :3000 >nul
if %errorlevel% equ 0 (
    echo ✓ 主服务器端口3000已监听
) else (
    echo ✗ 主服务器端口3000未监听
)

netstat -an | findstr :6656 >nul
if %errorlevel% equ 0 (
    echo ✓ WebSocket端口6656已监听
) else (
    echo ✗ WebSocket端口6656未监听
)

echo.
echo ========================================
echo 服务启动完成
echo ========================================
echo.
echo 访问地址:
echo   主页面: http://localhost:3000
echo   文件管理: http://localhost:3000/files
echo   WebSocket: ws://localhost:6656
echo.
echo 日志文件:
echo   主服务器: logs\server.log
echo   WebSocket: logs\websocket.log
echo   开发服务器: logs\dev.log
echo.
echo 管理命令:
echo   查看日志: type logs\server.log
echo   停止服务: stop-windows.bat
echo   查看状态: status-windows.bat
echo.

REM 询问是否打开浏览器
set /p open_browser="是否打开浏览器? (y/n): "
if /i "%open_browser%"=="y" (
    start http://localhost:3000
)

echo.
echo 按任意键退出...
pause >nul
