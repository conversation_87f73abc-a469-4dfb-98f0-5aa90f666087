'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams, useRouter } from 'next/navigation'
import Image from 'next/image'
import { FiUpload, FiFile, FiFolder, FiDownload, FiClock, FiTrash2, FiAlertTriangle, FiX, FiImage, FiFileText, FiVideo, FiMusic, FiChevronLeft, FiChevronRight, FiHome } from 'react-icons/fi'

// 文件预览模态框组件
const FilePreviewModal = ({ file, isOpen, onClose, formatSize, formatDate }) => {
  if (!isOpen || !file) return null;
  
  const isImage = /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i.test(file?.name || '');
  const isPDF = /\.pdf$/i.test(file?.name || '');
  const isVideo = /\.(mp4|webm|ogv|mov|avi)$/i.test(file?.name || '');
  const isAudio = /\.(mp3|wav|ogg|flac|aac)$/i.test(file?.name || '');
  
  // 获取文件URL（从文件代理转为直接访问）
  const fileUrl = file?.path;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="text-xl font-semibold truncate max-w-[calc(100%-80px)]">{file?.name}</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors rounded-full p-1 hover:bg-gray-100"
            aria-label="关闭"
          >
            <FiX size={24} />
          </button>
        </div>
        
        <div className="p-4 overflow-auto flex-grow flex items-center justify-center">
          {isImage && (
            <img 
              src={fileUrl} 
              alt={file?.name} 
              className="max-w-full max-h-[calc(80vh-120px)] object-contain"
            />
          )}
          
          {isPDF && (
            <iframe 
              src={fileUrl} 
              className="w-full h-[calc(80vh-120px)]" 
              title={file?.name}
            ></iframe>
          )}
          
          {isVideo && (
            <video 
              src={fileUrl} 
              controls 
              className="max-w-full max-h-[calc(80vh-120px)]"
            >
              您的浏览器不支持视频预览
            </video>
          )}
          
          {isAudio && (
            <audio 
              src={fileUrl} 
              controls 
              className="w-full"
            >
              您的浏览器不支持音频预览
            </audio>
          )}
          
          {!isImage && !isPDF && !isVideo && !isAudio && (
            <div className="text-center">
              <div className="w-24 h-24 mx-auto text-gray-400">
                <FiFileText size={96} />
              </div>
              <p className="mt-4 text-gray-600">无法预览此类型的文件</p>
              <a 
                href={fileUrl} 
                download={file?.name}
                className="mt-4 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                下载文件
              </a>
            </div>
          )}
        </div>
        
        <div className="border-t p-4 flex justify-between">
          <div className="text-sm text-gray-600">
            大小: {formatSize(file?.size || 0)} • 上传于: {formatDate(file?.modifiedAt)}
          </div>
          <a 
            href={fileUrl} 
            download={file?.name}
            className="text-blue-600 hover:text-blue-800 transition-colors"
          >
            下载
          </a>
        </div>
      </div>
    </div>
  );
};

// 文件上传组件
export default function UploadPortal() {
  const { id } = useParams()
  const router = useRouter()
  
  const [currentPath, setCurrentPath] = useState('')
  const [portalName, setPortalName] = useState('')
  const [expiryDate, setExpiryDate] = useState(null)
  const [files, setFiles] = useState([])
  const [folders, setFolders] = useState([])
  const [breadcrumbs, setBreadcrumbs] = useState([])
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState(null)
  const [deviceId, setDeviceId] = useState('')
  const [isDragging, setIsDragging] = useState(false)
  const [totalSize, setTotalSize] = useState(0)
  const [totalFiles, setTotalFiles] = useState(0)
  const [fileToDelete, setFileToDelete] = useState(null)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deletingFile, setDeletingFile] = useState(false)
  const [selectedFile, setSelectedFile] = useState(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const [isPortalValid, setIsPortalValid] = useState(true)
  const [syncStatuses, setSyncStatuses] = useState({})
  const [wsConnected, setWsConnected] = useState(false)
  const [ws, setWs] = useState(null)
  const [isMobile, setIsMobile] = useState(false)
  const [showUploadOptions, setShowUploadOptions] = useState(false)
  const [cameraPermission, setCameraPermission] = useState('unknown') // 'unknown', 'granted', 'denied', 'prompt'
  const [showPermissionGuide, setShowPermissionGuide] = useState(false)
  const [isHttps, setIsHttps] = useState(false)

  // 检测是否为移动设备
  const detectMobile = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera
    const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase())
    const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    const isSmallScreen = window.innerWidth <= 768
    
    return isMobileDevice || (hasTouch && isSmallScreen)
  }

  // 初始化设备检测
  useEffect(() => {
    setIsMobile(detectMobile())
    
    // 检测HTTPS环境
    setIsHttps(window.location.protocol === 'https:')
    
    // 检查摄像头权限
    checkCameraPermission()
    
    // 监听窗口大小变化
    const handleResize = () => {
      setIsMobile(detectMobile())
    }
    
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 检查摄像头权限状态
  const checkCameraPermission = async () => {
    try {
      // 检查浏览器支持
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setCameraPermission('unsupported')
        return
      }

      // 检查权限API支持
      if (navigator.permissions && navigator.permissions.query) {
        const permissionStatus = await navigator.permissions.query({ name: 'camera' })
        setCameraPermission(permissionStatus.state)
        
        // 监听权限状态变化
        permissionStatus.onchange = () => {
          setCameraPermission(permissionStatus.state)
        }
      } else {
        // 如果不支持权限API，设置为未知状态
        setCameraPermission('unknown')
      }
    } catch (error) {
      console.error('检查摄像头权限失败:', error)
      setCameraPermission('unknown')
    }
  }

  // 加载入口信息和文件列表
  const loadPortalData = useCallback(async () => {
    try {
      console.log('开始加载数据，当前路径:', currentPath);
      // 获取当前设备ID
      const currentDeviceId = deviceId || localStorage.getItem(`portal_device_${id}`);
      if (!currentDeviceId) {
        // If no device ID, show error or prompt? Assume error for now.
        setError('设备 ID 未找到，请先上传一个文件以注册设备。');
        setFiles([]);
        setFolders([]);
        setTotalFiles(0); 
        setTotalSize(0);
        return;
      }

      // 构建API URL，包含当前路径参数和设备ID
      let url = `/api/portal/${id}/device-files`
      const params = new URLSearchParams();
      
      if (currentPath) {
        params.append('path', currentPath);
      }
      params.append('deviceId', currentDeviceId);
      
      const queryString = params.toString();
      if (queryString) {
        url += `?${queryString}`;
      }
      
      console.log('请求数据URL:', url);
      const response = await fetch(url)
      const data = await response.json() 
      
      // Use HTTP status code to check for fetch errors first
      if (!response.ok) {
         // Use error message from API response if available
         throw new Error(data.error || `服务器错误: ${response.status}`);
      }
      
      console.log('获取到数据:', 
                 `文件夹数=${data.folders?.length || 0}`, 
                 `文件数=${data.files?.length || 0}`);
      
      // Now we can assume the data structure is correct
      setPortalName(data.portalName)
      setExpiryDate(data.expiresAt ? new Date(data.expiresAt) : null)
      setFiles(data.files || [])
      setFolders(data.folders || [])
      
      // === 修复：确保在portalName设置后再初始化状态 ===
      console.log('开始初始化同步状态...', {
        portalNameFromAPI: data.portalName,
        currentPortalName: portalName,
        deviceId: currentDeviceId
      });
      
      const newSyncStatuses = {};
      
      // 处理文件状态
      (data.files || []).forEach(file => {
        const filePath = currentPath ? `${currentPath}/${file.name}` : file.name;
        
        // === 修复：直接使用API返回的portalName构建完整路径 ===
        const fullLogicalPath = `${data.portalName}/${currentDeviceId}/${filePath}`;
        
        // 映射API返回的状态到syncStatuses格式
        let statusValue = '未同步'; // 默认状态
        if (file.status === 'synced' || file.status === 'uploaded') {
          statusValue = '已同步';
        } else if (file.status === 'pending') {
          statusValue = '未同步';
        } else if (file.status === 'uploading' || file.status === 'syncing') {
          statusValue = '同步中';
        } else if (file.status === 'failed') {
          statusValue = '同步失败';
        }
        
        newSyncStatuses[fullLogicalPath] = statusValue;
        console.log(`初始化文件状态: ${fullLogicalPath} -> ${statusValue} (原始: ${file.status})`);
      });
      
      // 处理文件夹状态（可选，如果需要）
      (data.folders || []).forEach(folder => {
        const folderPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
        
        // === 修复：直接使用API返回的portalName构建完整路径 ===
        const fullLogicalPath = `${data.portalName}/${currentDeviceId}/${folderPath}`;
        
        // 文件夹默认为未同步状态
        newSyncStatuses[fullLogicalPath] = '未同步';
        console.log(`初始化文件夹状态: ${fullLogicalPath} -> 未同步`);
      });
      
      // 更新syncStatuses状态（合并而不是覆盖，保留用户手动设置的状态）
      setSyncStatuses(prevStatuses => {
        console.log('更新前的状态:', prevStatuses);
        console.log('从API获取的状态:', newSyncStatuses);
        
        const mergedStatuses = { ...prevStatuses, ...newSyncStatuses };
        console.log('合并后的状态:', mergedStatuses);
        
        return mergedStatuses;
      });
      
      // 计算总文件数和总大小
      const files = data.files || [];
      const folders = data.folders || [];
      
      // 文件总数：当前页面显示的文件数 + 文件夹中的文件总数
      const totalFilesCount = files.length + folders.reduce((sum, folder) => sum + (folder.fileCount || 0), 0);
      
      // 总大小：所有文件大小 + 所有文件夹大小的总和
      const totalFilesSize = files.reduce((sum, file) => sum + (file.size || 0), 0) + 
                           folders.reduce((sum, folder) => sum + (folder.size || 0), 0);
      
      console.log(`计算统计数据: 总文件数=${totalFilesCount}, 总大小=${totalFilesSize}`);
      
      setTotalFiles(totalFilesCount);
      setTotalSize(totalFilesSize);
      setError(null); // Clear previous errors on success
      
      // 生成面包屑导航
      generateBreadcrumbs(currentPath)
    } catch (err) {
      console.error('加载上传入口数据失败:', err)
      setError(err.message || '无法连接到服务器')
      // Clear data on error
      setFiles([]);
      setFolders([]);
      setPortalName('');
      setExpiryDate(null);
      setTotalFiles(0);
      setTotalSize(0);
    }
  }, [id, currentPath, deviceId, portalName]) // 添加portalName依赖

  // 处理路径
  const getPathParts = (path) => {
    if (!path) return [];
    return path.split('/').filter(Boolean);
  };

  // 构建路径
  const buildPath = (parts) => {
    if (!parts || parts.length === 0) return '';
    return parts.join('/');
  };

  // 导航到文件夹
  const navigateToFolder = (targetPath) => {
    let newPath = '';
    
    // 处理返回根目录
    if (!targetPath || targetPath === '') {
      setCurrentPath('');
      return;
    }

    // 处理返回上级目录
    if (targetPath === '..') {
      const pathParts = getPathParts(currentPath);
      if (pathParts.length === 0) return;
      newPath = buildPath(pathParts.slice(0, -1));
    }
    // 处理面包屑导航点击（完整路径）
    else if (targetPath.includes('/')) {
      newPath = targetPath;
    }
    // 处理进入子文件夹
    else {
      const currentParts = getPathParts(currentPath);
      newPath = buildPath([...currentParts, targetPath]);
    }

    console.log(`导航到文件夹: ${newPath}`);
    // 设置新路径
    setCurrentPath(newPath);
  };

  // 生成面包屑导航
  const generateBreadcrumbs = (path) => {
    if (!path) {
      setBreadcrumbs([{ name: '根目录', path: '' }]);
      return;
    }

    const pathParts = path.split('/').filter(Boolean);
    const breadcrumbs = [{ name: '根目录', path: '' }];
    
    let currentPath = '';
    for (const part of pathParts) {
      currentPath = currentPath ? `${currentPath}/${part}` : part;
      breadcrumbs.push({
        name: part,
        path: currentPath,
        isFolder: true
      });
    }
    
    setBreadcrumbs(breadcrumbs);
  };

  // 处理面包屑导航点击
  const handleBreadcrumbClick = (path) => {
    // 如果点击根目录
    if (path === '') {
      setCurrentPath('');
      return;
    }

    // 获取当前路径的所有部分
    const currentParts = currentPath.split('/').filter(Boolean);
    // 获取目标路径的所有部分
    const targetParts = path.split('/').filter(Boolean);

    // 设置新路径
    setCurrentPath(path);
  };

  // 处理返回上级目录
  const handleBackToParent = () => {
    if (!currentPath) return;
    
    const pathParts = currentPath.split('/').filter(Boolean);
    if (pathParts.length === 0) return;
    
    // 移除最后一个部分
    pathParts.pop();
    const newPath = pathParts.join('/');
    setCurrentPath(newPath);
  };

  // 初始化加载
  useEffect(() => {
    const checkPortalStatus = async () => {
      if (!id) return; // 如果没有ID，则不执行检查
      try {
        const response = await fetch(`/api/portal/${id}/status`);
        if (response.ok) {
          const data = await response.json();
          if (!data.isValid) {
            setIsPortalValid(false);
            router.replace('/portal/invalid'); // 如果无效，跳转到提示页面
          } else {
            // 如果有效，则加载入口数据
            loadPortalData();
            // 添加调试日志
            console.log("Portal valid, loading data for portal:", id);
          }
        } else {
          // 如果API调用失败，也视为无效
          setIsPortalValid(false);
          router.replace('/portal/invalid');
        }
      } catch (error) {
        console.error('Error checking portal status:', error);
        setIsPortalValid(false);
        router.replace('/portal/invalid');
      }
    };

    checkPortalStatus();
  }, [id, router, loadPortalData]); // 添加loadPortalData为依赖项

  // 监听路径变化，重新加载数据
  useEffect(() => {
    if (deviceId && isPortalValid && id) {
      console.log("Path changed, reloading data. New path:", currentPath);
      loadPortalData();
    }
  }, [currentPath, deviceId, isPortalValid, loadPortalData, id]);

  // 递归遍历文件树（处理文件夹上传）
  const traverseFileTree = async (dataTransferItems) => {
    const fileList = []
    const pathList = []
    const queue = []
    
    console.log('开始处理文件夹上传...');
    
    // 首先将顶层条目添加到队列，并记录基础路径
    for (let i = 0; i < dataTransferItems.length; i++) {
      const entry = dataTransferItems[i].webkitGetAsEntry()
      if (entry) {
        console.log(`发现顶层条目:`, entry.name, entry.isDirectory ? '(文件夹)' : '(文件)');
        queue.push({
          entry: entry,
          path: '' // 顶层路径为空
        })
      }
    }
    
    setUploading(true)
    
    // 使用队列处理所有文件和目录
    while (queue.length > 0) {
      const { entry, path } = queue.shift()
      
      if (entry.isFile) {
        // 如果是文件，获取File对象并保存其路径
        const file = await new Promise((resolve) => {
          entry.file(resolve)
        })
        
        fileList.push(file)
        const fullPath = path ? `${path}/${entry.name}` : entry.name;
        pathList.push(fullPath)
        console.log(`添加文件: ${fullPath}, 大小: ${file.size} 字节`);
      } else if (entry.isDirectory) {
        // 构建目录路径
        const dirPath = path ? `${path}/${entry.name}` : entry.name
        console.log(`处理目录: ${dirPath}`);
        
        // 如果是目录，获取其中的所有条目
        const dirReader = entry.createReader()
        const entries = await new Promise((resolve) => {
          const result = []
          function readEntries() {
            dirReader.readEntries((entries) => {
              if (entries.length === 0) {
                resolve(result)
              } else {
                result.push(...entries)
                readEntries()
              }
            })
          }
          readEntries()
        })
        
        console.log(`目录 ${dirPath} 中找到 ${entries.length} 个条目`);
        
        // 将目录中的条目添加到队列，并保留相对路径
        for (const childEntry of entries) {
          queue.push({
            entry: childEntry,
            path: dirPath
          })
        }
      }
    }
    
    // 上传收集到的所有文件，同时传递路径信息
    if (fileList.length > 0) {
      console.log(`准备上传 ${fileList.length} 个文件，带有 ${pathList.length} 个路径信息`);
      await handleFileUpload(fileList, pathList)
    } else {
      setUploading(false)
      setError('未找到要上传的文件')
    }
  }

  // 处理文件上传
  const handleFileUpload = async (selectedFiles, filePaths = []) => {
    if (!selectedFiles || selectedFiles.length === 0) return
    
    setUploading(true)
    setUploadProgress(0)
    setError(null)
    
    try {
      const formData = new FormData()
      
      console.log(`开始上传 ${selectedFiles.length} 个文件`);
      
      // 计算将要上传的总文件大小（用于实时更新统计）
      const newFilesSize = Array.from(selectedFiles).reduce((sum, file) => sum + file.size, 0);
      
      // Add all files to FormData
      for (let i = 0; i < selectedFiles.length; i++) {
        formData.append('file', selectedFiles[i])
        
        // If we have paths for folder uploads, include them
        if (filePaths[i]) {
          formData.append('paths', filePaths[i])
          console.log(`文件 ${i+1}: ${selectedFiles[i].name}, 路径: ${filePaths[i]}`);
        } else {
          console.log(`文件 ${i+1}: ${selectedFiles[i].name}, 无路径信息`);
        }
      }

      // 添加当前的设备ID如果存在
      const currentDeviceId = deviceId || localStorage.getItem(`portal_device_${id}`);
      if (currentDeviceId) {
        formData.append('deviceId', currentDeviceId);
      }
      
      // Send upload request
      console.log(`发送上传请求到 /api/portal/${id}/upload, 当前路径: ${currentPath}`);
      if (currentPath) {
        formData.append('currentPath', currentPath);
      }
      
      const response = await fetch(`/api/portal/${id}/upload`, {
        method: 'POST',
        body: formData,
      })
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || '上传失败')
      }
      
      console.log('上传成功，服务器响应:', result);
      
      // 保存设备ID到state和localStorage
      if (result.deviceId) {
        setDeviceId(result.deviceId);
        localStorage.setItem(`portal_device_${id}`, result.deviceId);
      }
      
      // 上传成功后刷新文件列表
      console.log('重新加载文件列表...');
      await loadPortalData();
      
      // 如果API没有返回新的文件列表，则手动更新统计数据
      if (!result.refreshed) {
        setTotalFiles(prev => prev + selectedFiles.length);
        setTotalSize(prev => prev + newFilesSize);
      }
    } catch (err) {
      console.error('上传文件失败:', err)
      setError(err.message || '上传文件时发生错误')
    } finally {
      setUploading(false)
      setUploadProgress(100)
      // 重置进度条
      setTimeout(() => setUploadProgress(0), 1000)
    }
  }

  // 处理文件选择
  const handleFileSelect = (e) => {
    const selectedFiles = e.target.files
    if (selectedFiles && selectedFiles.length > 0) {
      handleFileUpload(selectedFiles)
    }
  }

  // 处理拍摄功能
  const handleCamera = async () => {
    try {
      setShowUploadOptions(false)
      
      // 首先检查HTTPS环境
      if (!isHttps && window.location.hostname !== 'localhost') {
        setError('摄像头功能需要HTTPS环境。请使用HTTPS访问此页面，或在本地环境(localhost)中使用。')
        setShowPermissionGuide(true)
        return
      }
      
      // 检查是否为移动设备
      if (!isMobile) {
        setError('拍摄功能仅在移动设备上可用')
        return
      }
      
      // 检查浏览器是否支持摄像头
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        setError('您的浏览器不支持摄像头功能')
        return
      }

      // 检查权限状态
      if (cameraPermission === 'denied') {
        setError('摄像头权限已被拒绝。请在浏览器设置中允许摄像头访问权限。')
        setShowPermissionGuide(true)
        return
      }

      if (cameraPermission === 'unsupported') {
        setError('您的浏览器不支持摄像头权限管理')
        return
      }

      // 如果权限未知或需要提示，先显示权限说明
      if (cameraPermission === 'unknown' || cameraPermission === 'prompt') {
        setShowPermissionGuide(true)
        return
      }

      // 如果权限已授予，直接调用系统相机
      document.getElementById('cameraInput').click()
      
    } catch (error) {
      console.error('启动系统相机失败:', error)
      setError('启动系统相机失败: ' + error.message)
    }
  }

  // 请求摄像头权限并启动系统相机
  const requestCameraPermissionAndStart = async () => {
    try {
      setShowPermissionGuide(false)
      setShowUploadOptions(false)  // 关闭上传选择模态框
      setError(null)
      
      // 请求摄像头权限
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // 优先使用后置摄像头
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      })

      // 权限获取成功，更新状态
      setCameraPermission('granted')
      
      // 立即停止临时流，然后调用系统相机
      stream.getTracks().forEach(track => track.stop())
      
      // 直接调用系统相机
      document.getElementById('cameraInput').click()

    } catch (error) {
      console.error('请求摄像头权限失败:', error)
      setCameraPermission('denied')
      
      if (error.name === 'NotAllowedError') {
        setError('摄像头权限被拒绝。请刷新页面并允许摄像头访问，或在浏览器设置中启用摄像头权限。')
      } else if (error.name === 'NotFoundError') {
        setError('未找到摄像头设备。请确保您的设备有可用的摄像头。')
      } else if (error.name === 'NotSupportedError') {
        setError('您的浏览器不支持摄像头功能，或当前环境不是HTTPS。')
      } else {
        setError('无法访问摄像头: ' + error.message)
      }
    }
  }

  // 处理系统相机拍摄结果
  const handleCameraCapture = (e) => {
    const file = e.target.files[0]
    if (file) {
      // 关闭上传选择模态框
      setShowUploadOptions(false)
      
      // 重置input以允许重复选择同一文件
      e.target.value = ''
      
      // 上传拍摄的照片
      handleFileUpload([file])
    }
  }

  // 显示上传选项（移动端）
  const showMobileUploadOptions = () => {
    setShowUploadOptions(true)
  }

  // 选择文件（移动端）
  const handleSelectFiles = () => {
    setShowUploadOptions(false)
    document.getElementById('fileInput').click()
  }

  // 处理拖放
  const handleDragOver = (e) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e) => {
    e.preventDefault()
    setIsDragging(false)
    
    const items = e.dataTransfer.items
    const files = e.dataTransfer.files
    
    // 如果有目录，需要用不同的处理方式
    if (items && items[0].webkitGetAsEntry) {
      traverseFileTree(items)
    } else if (files && files.length > 0) {
      handleFileUpload(files)
    }
  }

  // 处理粘贴事件（从剪贴板上传）
  const handlePaste = (e) => {
    if (e.clipboardData && e.clipboardData.files.length > 0) {
      e.preventDefault()
      handleFileUpload(e.clipboardData.files)
    }
  }

  // 格式化文件大小
  const formatSize = (bytes) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化日期
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleString()
  }

  // 检查入口是否过期
  const isExpired = expiryDate && new Date() > expiryDate

  // 提取文件图标类型
  const getFileIcon = (fileName) => {
    const ext = fileName.split('.').pop().toLowerCase()
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp']
    
    if (imageExts.includes(ext)) {
      return 'image'
    }
    
    const docExts = ['doc', 'docx', 'pdf', 'txt', 'rtf']
    if (docExts.includes(ext)) {
      return 'document'
    }
    
    const archiveExts = ['zip', 'rar', '7z', 'tar', 'gz']
    if (archiveExts.includes(ext)) {
      return 'archive'
    }
    
    return 'file'
  }

  // 处理文件删除
  const handleDeleteFile = async (file) => {
    if (!file || deletingFile) return;
    
    setDeletingFile(true);
    setError(null);
    
    try {
      // 从文件对象、组件状态或localStorage获取设备ID
      const currentDeviceId = file.deviceId || deviceId || localStorage.getItem(`portal_device_${id}`);
      if (!currentDeviceId) {
        throw new Error('您没有权限删除此文件');
      }

      // 构建完整的文件路径
      const filePath = currentPath 
        ? `${currentPath}/${file.name}`
        : file.name;

      const response = await fetch(`/api/portal/${id}/delete-file`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: currentDeviceId,
          filePath: filePath,
          fileSize: file.size || 0
        }),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || '删除文件失败');
      }
      
      // 从文件列表中移除已删除的文件
      setFiles(files.filter(f => f.name !== file.name));
      
      // 更新统计信息
      setTotalFiles(prev => Math.max(0, prev - 1));
      setTotalSize(prev => Math.max(0, prev - (file.size || 0)));
      
      // 关闭确认对话框
      setShowDeleteModal(false);
      setFileToDelete(null);
    } catch (err) {
      console.error('删除文件出错:', err);
      setError(err.message || '删除文件时出错');
    } finally {
      setDeletingFile(false);
    }
  };
  
  // 打开删除确认对话框
  const confirmDelete = (item, isFolder = false) => {
    setFileToDelete({ ...item, isFolder });
    setShowDeleteModal(true);
  };

  // 处理文件预览
  const handleFilePreview = (file) => {
    setSelectedFile(file)
    setIsPreviewOpen(true)
  }
  
  // 关闭预览模态框
  const closePreview = () => {
    setIsPreviewOpen(false)
  }

  // 处理文件夹删除
  const handleDeleteFolder = async (folder) => {
    if (!folder || deletingFile) return;
    
    setDeletingFile(true);
    setError(null);
    
    try {
      // 从组件状态或localStorage获取设备ID
      const currentDeviceId = deviceId || localStorage.getItem(`portal_device_${id}`);
      if (!currentDeviceId) {
        throw new Error('您没有权限删除此文件夹');
      }

      // 构建完整的文件夹路径
      const folderPath = currentPath 
        ? `${currentPath}/${folder.name}`
        : folder.name;

      const response = await fetch(`/api/portal/${id}/delete-file`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: currentDeviceId,
          folderPath: folderPath,
          isFolder: true
        }),
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || '删除文件夹失败');
      }
      
      // 从文件夹列表中移除已删除的文件夹
      setFolders(folders.filter(f => f.name !== folder.name));
      
      // 更新统计信息
      setTotalFiles(prev => Math.max(0, prev - (folder.fileCount || 0)));
      setTotalSize(prev => Math.max(0, prev - (folder.size || 0)));
      
      // 关闭确认对话框
      setShowDeleteModal(false);
      setFileToDelete(null);
    } catch (err) {
      console.error('删除文件夹出错:', err);
      setError(err.message || '删除文件夹时出错');
    } finally {
      setDeletingFile(false);
    }
  };

  // 处理状态更改
  const handleStatusChange = async (file) => {
    try {
      const newStatus = file.status === 'synced' ? 'pending' : 'synced';
      
      const response = await fetch(`/api/portal/${id}/file-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceId: file.deviceId || deviceId,
          filePath: currentPath ? `${currentPath}/${file.name}` : file.name,
          status: newStatus
        }),
      });
      
      if (response.ok) {
        // 更新本地文件列表中的状态
        setFiles(prevFiles => 
          prevFiles.map(f => 
            f.name === file.name ? { ...f, status: newStatus } : f
          )
        );
        setError(`已${newStatus === 'synced' ? '标记为已同步' : '标记为未同步'}`);
        setTimeout(() => setError(null), 2000);
      } else {
        const data = await response.json();
        setError('更改状态失败: ' + (data.error || '未知错误'));
      }
    } catch (err) {
      setError('更改状态错误: ' + err.message);
    }
  };

  // 添加更新文件状态为已同步的函数（仅用于真正同步完成时）
  const markFileAsSynced = async (filePath) => {
    try {
      console.log(`标记文件为已同步: ${filePath}`);
      
      // === 修复：构建完整的逻辑路径格式 ===
      const fullLogicalPath = constructFullLogicalPath(filePath);
      console.log(`标记文件为已同步，完整路径: ${fullLogicalPath}`);
      
      // 立即更新本地状态显示 - 使用完整路径作为键
      setSyncStatuses(prevStatuses => {
        console.log('更新前的状态:', prevStatuses);
        const newStatuses = {
          ...prevStatuses,
          [fullLogicalPath]: '已同步', // 使用完整路径
        };
        console.log('更新后的状态:', newStatuses);
        return newStatuses;
      });
      
      // 优先使用WebSocket更新状态
      if (wsConnected) {
        const success = sendWebSocketMessage({
          type: 'update_file_status',
          filename: fullLogicalPath, // 使用完整路径
          status: '已同步', // 使用WebSocket服务器的中文状态值
          timestamp: Date.now()
        });
        
        if (success) {
          console.log(`通过WebSocket更新文件状态: ${fullLogicalPath} -> 已同步`);
          showToast(`文件 ${filePath.split('/').pop()} 已标记为已同步`, 'success');
          return;
        } else {
          console.warn('WebSocket发送失败，降级到HTTP API');
        }
      }
      
      // 备用方案：通过fetch API调用状态更新接口
      const response = await fetch('/api/admin/files/status', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filePath: fullLogicalPath, // 使用完整路径
          status: 'uploaded' // 后端使用的状态值
        }),
      });
      
      if (response.ok) {
        console.log(`通过HTTP API更新文件状态成功: ${fullLogicalPath} -> 已同步`);
        showToast(`文件 ${filePath.split('/').pop()} 已标记为已同步`, 'success');
      } else {
        const errorData = await response.json();
        console.error('状态更新API调用失败:', errorData);
        // 如果API调用失败，回滚本地状态
        setSyncStatuses(prevStatuses => ({
          ...prevStatuses,
          [fullLogicalPath]: '未同步',
        }));
        showToast(`更新 ${filePath.split('/').pop()} 状态失败`, 'error');
      }
    } catch (error) {
      console.error('更新文件状态时出错:', error);
      const fullLogicalPath = constructFullLogicalPath(filePath);
      // 如果出错，回滚本地状态
      setSyncStatuses(prevStatuses => ({
        ...prevStatuses,
        [fullLogicalPath]: '未同步',
      }));
      showToast(`更新 ${filePath.split('/').pop()} 状态失败`, 'error');
    }
  };

  // 添加更新文件状态为未同步的函数（新上传的文件）
  const updateFileStatusToUnsynced = async (filePath) => {
    try {
      console.log(`正在将新上传文件状态设置为未同步: ${filePath}`);
      
      // === 修复：构建完整的逻辑路径格式 ===
      const fullLogicalPath = constructFullLogicalPath(filePath);
      console.log(`新上传文件状态设置，完整路径: ${fullLogicalPath}`);
      
      // === 第一步：立即更新本地状态显示为"未同步" ===
      setSyncStatuses(prevStatuses => {
        console.log('状态更新前:', prevStatuses);
        const newStatuses = {
          ...prevStatuses,
          [fullLogicalPath]: '未同步', // 新上传的文件必须是未同步状态，使用完整路径
        };
        console.log('状态更新后:', newStatuses);
        return newStatuses;
      });
      
      // === 第二步：强制清理可能存在的旧状态（多重清理） ===
      console.log(`[新上传] 开始强制清理旧状态: ${fullLogicalPath}`);
      
      // 2.1 通过WebSocket发送删除旧状态的请求
      if (wsConnected) {
        const cleanupSuccess = sendWebSocketMessage({
          type: 'remove_file_status',
          filename: fullLogicalPath, // 使用完整路径
          isFolder: false,
          timestamp: Date.now(),
          reason: 'pre_upload_cleanup'
        });
        
        if (cleanupSuccess) {
          console.log(`[新上传] 已发送状态清理请求: ${fullLogicalPath}`);
          // 给状态清理足够的时间
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
      
      // 2.2 再次发送强制清理请求（确保清理彻底）
      if (wsConnected) {
        const secondCleanupSuccess = sendWebSocketMessage({
          type: 'remove_file_status',
          filename: fullLogicalPath, // 使用完整路径
          isFolder: false,
          timestamp: Date.now(),
          reason: 'force_pre_upload_cleanup'
        });
        
        if (secondCleanupSuccess) {
          console.log(`[新上传] 已发送第二次状态清理请求: ${fullLogicalPath}`);
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }
      
      // === 第三步：多重状态设置确保"未同步" ===
      let statusUpdateSuccess = false;
      
      // 3.1 第一次WebSocket状态设置
      if (wsConnected) {
        const success = sendWebSocketMessage({
          type: 'update_file_status',
          filename: fullLogicalPath, // 使用完整路径
          status: '未同步',
          timestamp: Date.now(),
          forceUpdate: true,
          isNewUpload: true,
          reason: 'new_file_upload'
        });
        
        if (success) {
          console.log(`[新上传] 第一次WebSocket状态设置: ${fullLogicalPath} -> 未同步`);
          statusUpdateSuccess = true;
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
      
      // 3.2 第二次WebSocket状态设置（双重保险）
      if (wsConnected) {
        const secondSuccess = sendWebSocketMessage({
          type: 'update_file_status',
          filename: fullLogicalPath, // 使用完整路径
          status: '未同步',
          timestamp: Date.now(),
          forceUpdate: true,
          isNewUpload: true,
          reason: 'new_file_upload_confirmation'
        });
        
        if (secondSuccess) {
          console.log(`[新上传] 第二次WebSocket状态设置: ${fullLogicalPath} -> 未同步`);
          statusUpdateSuccess = true;
        }
      }
      
      // === 第四步：HTTP API备用状态设置 ===
      if (!statusUpdateSuccess || !wsConnected) {
        console.log(`[新上传] 使用HTTP API设置状态: ${fullLogicalPath}`);
        
        try {
          // 4.1 先尝试删除旧状态
          const deleteResponse = await fetch('/api/admin/files/status', {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              filePath: fullLogicalPath, // 使用完整路径
              reason: 'new_upload_cleanup'
            }),
          });
          
          if (deleteResponse.ok) {
            console.log(`[新上传] HTTP API删除旧状态成功: ${fullLogicalPath}`);
          }
          
          // 4.2 设置新的"未同步"状态
          const response = await fetch('/api/admin/files/status', {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              filePath: fullLogicalPath, // 使用完整路径
              status: 'pending',
              forceUpdate: true,
              isNewUpload: true,
              reason: 'new_file_upload'
            }),
          });
          
          if (response.ok) {
            console.log(`[新上传] HTTP API状态设置成功: ${fullLogicalPath} -> 未同步`);
            statusUpdateSuccess = true;
          } else {
            const errorData = await response.json();
            console.error(`[新上传] HTTP API状态设置失败:`, errorData);
          }
        } catch (httpError) {
          console.error(`[新上传] HTTP API调用异常:`, httpError);
        }
      }
      
      // === 第五步：验证状态设置结果 ===
      if (statusUpdateSuccess) {
        // 5.1 再次确认本地状态
        setSyncStatuses(prevStatuses => ({
          ...prevStatuses,
          [fullLogicalPath]: '未同步' // 使用完整路径
        }));
        
        // 5.2 显示成功消息
        const fileName = filePath.split('/').pop();
        console.log(`[新上传] 文件状态设置完成: ${fileName} -> 未同步`, {
          filePath: filePath,
          fullLogicalPath: fullLogicalPath,
          finalStatus: '未同步',
          method: wsConnected ? 'WebSocket' : 'HTTP API'
        });
        
        showToast(`文件 ${fileName} 已上传，状态：未同步`, 'success');
      } else {
        // 5.3 设置失败但保持未同步状态（安全的默认值）
        const fileName = filePath.split('/').pop();
        console.warn(`[新上传] 状态设置可能失败，但保持未同步状态: ${fileName}`);
        showToast(`文件 ${fileName} 已上传，请手动检查同步状态`, 'warning');
      }
      
    } catch (error) {
      console.error(`[新上传] 状态设置过程出错:`, error);
      
      // 即使出错，也确保本地显示为未同步状态
      const fullLogicalPath = constructFullLogicalPath(filePath);
      setSyncStatuses(prevStatuses => ({
        ...prevStatuses,
        [fullLogicalPath]: '未同步' // 使用完整路径
      }));
      
      const fileName = filePath.split('/').pop();
      showToast(`文件 ${fileName} 已上传，状态设置遇到问题，请手动检查`, 'warning');
    }
  };
  
  // === 新增：构建完整逻辑路径的辅助函数 ===
  const constructFullLogicalPath = (filePath) => {
    try {
      // === 修复：添加防护措施，确保portalName和deviceId存在 ===
      if (!portalName || !deviceId) {
        console.warn(`构建完整逻辑路径时缺少必要信息: portalName=${portalName}, deviceId=${deviceId}`);
        return filePath; // 如果缺少信息，返回原路径
      }
      
      // 如果filePath已经是完整格式（包含portal名称），直接返回
      if (filePath.includes(portalName) && filePath.includes(deviceId)) {
        console.log(`路径已是完整格式: ${filePath}`);
        return filePath;
      }
      
      // 构建完整的逻辑路径：portal名称/deviceId/文件路径
      const fullPath = `${portalName}/${deviceId}/${filePath}`;
      
      console.log(`构建完整逻辑路径: ${filePath} -> ${fullPath}`, {
        portalName,
        deviceId,
        originalPath: filePath
      });
      
      return fullPath;
    } catch (error) {
      console.error('构建完整逻辑路径失败:', error);
      return filePath; // 出错时返回原路径
    }
  };

  // === 新增：WebSocket相关功能 ===
  
  // WebSocket消息发送函数
  const sendWebSocketMessage = (message) => {
    try {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
        console.log('WebSocket消息已发送:', message);
        return true;
      } else {
        console.warn('WebSocket未连接，无法发送消息:', message);
        return false;
      }
    } catch (error) {
      console.error('WebSocket发送消息失败:', error);
      return false;
    }
  };
  
  // 初始化WebSocket连接
  useEffect(() => {
    if (!id) return; // 如果没有ID则不连接
    
    try {
      const wsPort = process.env.NEXT_PUBLIC_WS_PORT || 6656;
      const wsUrl = `ws://192.168.100.110:${wsPort}`;
      const newWs = new WebSocket(wsUrl);
      
      newWs.onopen = () => {
        console.log('Portal WebSocket连接已建立');
        setWsConnected(true);
        setWs(newWs);
        
        // 发送初始化消息
        newWs.send(JSON.stringify({
          type: 'init',
          client: 'portal',
          portalId: id,
          version: '1.0'
        }));
      };
      
      newWs.onmessage = (event) => {
        try {
          const messageData = JSON.parse(event.data);
          console.log('收到WebSocket消息:', messageData);
          
          // 处理不同类型的消息
          switch (messageData.type) {
            case 'file_status_updated':
              // 文件状态更新
              if (messageData.filePath && messageData.status) {
                console.log(`文件状态更新: ${messageData.filePath} -> ${messageData.status}`);
                
                setSyncStatuses(prevStatuses => {
                  const newStatuses = {
                    ...prevStatuses,
                    [messageData.filePath]: messageData.status,
                  };
                  console.log('WebSocket更新后的状态:', newStatuses);
                  return newStatuses;
                });
                
                // 显示状态更新提示
                const fileName = messageData.filePath.split('/').pop();
                const statusText = messageData.status === '已同步' ? '已同步' : 
                                 messageData.status === '同步中' ? '同步中' : 
                                 messageData.status === '同步失败' ? '同步失败' : '未同步';
                showToast(`文件 ${fileName} 状态已更新为: ${statusText}`, 
                         messageData.status === '已同步' ? 'success' : 
                         messageData.status === '同步失败' ? 'error' : 'info');
              }
              break;
              
            case 'file_status_removed':
              // 文件状态移除
              if (messageData.filePath) {
                console.log(`文件状态已移除: ${messageData.filePath}`);
                setSyncStatuses(prevStatuses => {
                  const newStatuses = { ...prevStatuses };
                  delete newStatuses[messageData.filePath];
                  return newStatuses;
                });
              }
              break;
              
            case 'init_response':
              console.log('WebSocket初始化响应:', messageData.message);
              break;
              
            default:
              console.log('未处理的WebSocket消息类型:', messageData.type);
              break;
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };
      
      newWs.onclose = () => {
        console.log('Portal WebSocket连接已关闭');
        setWsConnected(false);
        setWs(null);
      };
      
      newWs.onerror = (error) => {
        console.error('Portal WebSocket连接错误:', error);
        setWsConnected(false);
      };
      
      // 清理函数
      return () => {
        if (newWs.readyState === WebSocket.OPEN) {
          newWs.close();
        }
      };
    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      setWsConnected(false);
    }
  }, [id]);
  
  // 添加Toast显示函数
  const showToast = (message, type = 'info') => {
    console.log(`[Toast ${type.toUpperCase()}] ${message}`);
    // 这里可以集成实际的Toast组件
    // 暂时使用console.log作为占位符
  };

  // 仅当入口有效时渲染上传界面
  if (!isPortalValid) {
    return null; // 或者可以显示一个加载状态，直到检查完成
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
        {/* 文件预览模态框 */}
        <FilePreviewModal
          file={selectedFile}
          isOpen={isPreviewOpen}
          onClose={closePreview}
          formatSize={formatSize}
          formatDate={formatDate}
        />
        
        {/* 移动端上传选项模态框 */}
        {showUploadOptions && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-sm w-full mx-4">
              <div className="p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-6 text-center">选择上传方式</h3>
                
                <div className="space-y-4">
                  <button
                    onClick={handleCamera}
                    disabled={cameraPermission === 'unsupported' || (!isHttps && window.location.hostname !== 'localhost')}
                    className={`w-full flex items-center justify-center px-4 py-3 rounded-lg transition-colors ${
                      cameraPermission === 'unsupported' || (!isHttps && window.location.hostname !== 'localhost')
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-blue-500 text-white hover:bg-blue-600'
                    }`}
                  >
                    <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0118.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <div className="text-left">
                      <div>拍摄照片</div>
                      {cameraPermission === 'unsupported' ? (
                        <div className="text-xs opacity-75">不支持</div>
                      ) : !isHttps && window.location.hostname !== 'localhost' ? (
                        <div className="text-xs opacity-75">需要HTTPS</div>
                      ) : cameraPermission === 'denied' ? (
                        <div className="text-xs opacity-75">权限被拒绝</div>
                      ) : cameraPermission === 'granted' ? (
                        <div className="text-xs opacity-75">使用系统相机</div>
                      ) : (
                        <div className="text-xs opacity-75">需要权限</div>
                      )}
                    </div>
                  </button>
                  
                  <button
                    onClick={handleSelectFiles}
                    className="w-full flex items-center justify-center px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
                  >
                    <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                    </svg>
                    选择文件
                  </button>
                </div>
                
                <div className="mt-6 text-center">
                  <button
                    onClick={() => setShowUploadOptions(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    取消
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* 摄像头权限引导模态框 */}
        {showPermissionGuide && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <svg className="w-8 h-8 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0118.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <h3 className="text-xl font-semibold text-gray-900">摄像头权限</h3>
                </div>
                
                {/* 环境检查提示 */}
                {!isHttps && window.location.hostname !== 'localhost' && (
                  <div className="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                    <div className="flex items-start">
                      <svg className="w-5 h-5 text-orange-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <div>
                        <p className="text-sm font-medium text-orange-800">需要HTTPS环境</p>
                        <p className="text-sm text-orange-700">现代浏览器要求在HTTPS环境下才能使用摄像头功能。请使用HTTPS访问此页面。</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* 权限状态显示 */}
                <div className="mb-4">
                  <div className="flex items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">当前权限状态:</span>
                    <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                      cameraPermission === 'granted' ? 'bg-green-100 text-green-800' :
                      cameraPermission === 'denied' ? 'bg-red-100 text-red-800' :
                      cameraPermission === 'prompt' ? 'bg-yellow-100 text-yellow-800' :
                      cameraPermission === 'unsupported' ? 'bg-gray-100 text-gray-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {cameraPermission === 'granted' ? '已授权' :
                       cameraPermission === 'denied' ? '已拒绝' :
                       cameraPermission === 'prompt' ? '需要授权' :
                       cameraPermission === 'unsupported' ? '不支持' :
                       '未知'}
                    </span>
                  </div>
                </div>

                {/* 说明内容 */}
                <div className="mb-6">
                  {cameraPermission === 'denied' ? (
                    <div>
                      <p className="text-sm text-gray-600 mb-3">
                        摄像头权限已被拒绝。请按以下步骤重新启用权限：
                      </p>
                      <ol className="text-sm text-gray-600 space-y-2 ml-4">
                        <li>1. 点击地址栏左侧的锁图标或摄像头图标</li>
                        <li>2. 选择"允许"摄像头访问</li>
                        <li>3. 刷新页面并重试</li>
                      </ol>
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm text-gray-600 mb-3">
                        我们需要访问您的摄像头来拍摄照片。授权后将直接打开系统相机界面。
                      </p>
                      <div className="text-sm text-gray-600 space-y-1">
                        <div className="flex items-center">
                          <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          使用系统原生相机界面
                        </div>
                        <div className="flex items-center">
                          <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          只在您主动拍摄时使用摄像头
                        </div>
                        <div className="flex items-center">
                          <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          您可以随时撤销权限
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                
                <div className="flex justify-between space-x-3">
                  <button
                    onClick={() => setShowPermissionGuide(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    取消
                  </button>
                  
                  {cameraPermission === 'denied' ? (
                    <button
                      onClick={() => window.location.reload()}
                      className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                      刷新页面
                    </button>
                  ) : (isHttps || window.location.hostname === 'localhost') ? (
                    <button
                      onClick={requestCameraPermissionAndStart}
                      className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                      允许并拍摄
                    </button>
                  ) : (
                    <button
                      onClick={() => {
                        const httpsUrl = window.location.href.replace('http://', 'https://')
                        window.location.href = httpsUrl
                      }}
                      className="flex-1 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
                    >
                      切换到HTTPS
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* 上传入口标题 */}
        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6 text-white">
          <h1 className="text-2xl font-bold flex items-center">
            <FiFolder className="mr-2" size={24} />
            {portalName || '上传入口'}
          </h1>
          
          {expiryDate && (
            <div className="mt-2 flex items-center text-white/80">
              <FiClock className="mr-1" size={16} />
              {isExpired ? (
                <span className="text-red-200">已过期 ({formatDate(expiryDate)})</span>
              ) : (
                <span>有效期至: {formatDate(expiryDate)}</span>
              )}
            </div>
          )}
          
          <div className="mt-4 flex space-x-4 text-sm">
            <div className="bg-white/20 rounded-lg px-3 py-1">
              总文件: {totalFiles} 个
            </div>
            <div className="bg-white/20 rounded-lg px-3 py-1">
              总大小: {formatSize(totalSize)}
            </div>
          </div>
        </div>
        
        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 text-red-700">
            <p>{error}</p>
          </div>
        )}
        
        {/* 开发调试信息 */}
        <div className="text-xs text-gray-400 p-2 hidden">
          <p>当前路径: {currentPath || '根目录'}</p>
          <p>设备ID: {deviceId}</p>
          <p>文件数: {files.length}</p>
          <p>文件夹数: {folders.length}</p>
        </div>
        
        {/* 上传区域 */}
        {!isExpired && (
          <div className="p-6 border-b border-gray-200">
            <div 
              className={`border-2 border-dashed rounded-lg p-12 text-center ${
                isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onPaste={handlePaste}
              onClick={() => isMobile ? showMobileUploadOptions() : document.getElementById('fileInput').click()}
            >
              <input
                id="fileInput"
                type="file"
                multiple
                onChange={handleFileSelect}
                className="hidden"
              />
              
              {/* 系统相机输入 */}
              <input
                id="cameraInput"
                type="file"
                accept="image/*"
                capture="environment"
                onChange={handleCameraCapture}
                className="hidden"
              />
              
              <FiUpload className="mx-auto mb-4 text-blue-500" size={48} />
              
              <div className="text-lg font-medium text-gray-700">
                {uploading ? '正在上传文件...' : (
                  isMobile ? '点击选择上传方式' : '点击或拖拽文件到此处上传'
                )}
              </div>
              
              <p className="mt-2 text-sm text-gray-500">
                {isMobile ? '支持拍摄照片或选择文件上传' : '支持单个文件、多个文件或整个文件夹'}
              </p>
              
              {!isMobile && (
                <p className="mt-1 text-xs text-gray-400">
                  也可以使用Ctrl+V粘贴截图或剪贴板中的文件
                </p>
              )}
              
              {/* 上传进度条 */}
              {uploadProgress > 0 && (
                <div className="mt-4 mx-auto max-w-md">
                  <div className="bg-gray-200 rounded-full h-2.5 overflow-hidden">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        
        {/* 面包屑导航 */}
        <div className="border-b border-gray-200 px-6 py-3">
          <nav className="flex items-center text-sm">
            {breadcrumbs.map((crumb, index) => (
              <div key={index} className="flex items-center">
                {index > 0 && <FiChevronRight className="mx-2 text-gray-400" size={16} />}
                <button
                  onClick={() => handleBreadcrumbClick(crumb.path)}
                  className={`hover:text-blue-600 transition-colors ${
                    index === breadcrumbs.length - 1
                      ? 'font-medium text-gray-800'
                      : 'text-gray-600'
                  }`}
                >
                  {index === 0 ? (
                    <FiHome className="inline mr-1" size={16} />
                  ) : null}
                  {crumb.name}
                </button>
              </div>
            ))}
          </nav>
        </div>
        
        {/* 文件和文件夹统一列表 */}
        <div className="divide-y divide-gray-200">
          <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-800">
                {currentPath ? '当前文件夹内容' : '文件列表'} ({folders.length + files.length})
              </h2>
              {currentPath && (
                <button
                  onClick={handleBackToParent}
                  className="flex items-center text-sm text-blue-600 hover:text-blue-800"
                >
                  <FiChevronLeft className="mr-1" size={16} />
                  返回上级文件夹
                </button>
              )}
            </div>
          </div>
          
          {folders.length === 0 && files.length === 0 ? (
            <div className="py-12 text-center text-gray-500">
              {isExpired ? 
                '此上传入口已过期' : 
                uploading ? '正在上传文件...' : '暂无文件或文件夹'
              }
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {/* 文件夹列表 */}
              {folders.map((folder, index) => (
                <div key={`folder-${index}`} className="flex items-center px-4 sm:px-6 py-4 hover:bg-gray-50">
                  <div 
                    className="flex-shrink-0 mr-3 sm:mr-4 cursor-pointer"
                    onClick={() => {
                      console.log("文件夹点击:", folder.name, "相对路径:", folder.relativePath);
                      if (folder.relativePath) {
                        navigateToFolder(folder.relativePath);
                      } else {
                        // 后备方案，如果没有相对路径，尝试使用名称
                        navigateToFolder(folder.name);
                      }
                    }}
                  >
                    <div className="w-12 h-12 flex items-center justify-center text-blue-500 border border-gray-200 rounded">
                      <FiFolder size={24} />
                    </div>
                  </div>
                  
                  <div 
                    className="flex-1 min-w-0 cursor-pointer"
                    onClick={() => {
                      console.log("文件夹点击:", folder.name, "相对路径:", folder.relativePath);
                      if (folder.relativePath) {
                        navigateToFolder(folder.relativePath);
                      } else {
                        // 后备方案，如果没有相对路径，尝试使用名称
                        navigateToFolder(folder.name);
                      }
                    }}
                  >
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {folder.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {folder.fileCount || 0} 个文件 • {formatSize(folder.size || 0)}
                    </p>
                  </div>

                  {/* 文件夹操作按钮 */}
                  <div className="ml-2 sm:ml-4 flex space-x-1">
                    <button
                      onClick={() => confirmDelete(folder, true)}
                      className="p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-full"
                      title="删除文件夹"
                      disabled={deletingFile}
                    >
                      <FiTrash2 size={20} />
                    </button>
                  </div>
                </div>
              ))}
              
              {/* 文件列表 */}
              {files.map((file, index) => (
                <div key={`file-${index}`} className="flex items-center px-4 sm:px-6 py-4 hover:bg-gray-50">
                  <div 
                    className="flex-shrink-0 mr-3 sm:mr-4 cursor-pointer" 
                    onClick={() => handleFilePreview(file)}
                  >
                    {getFileIcon(file.name) === 'image' ? (
                      <div className="w-12 h-12 rounded border border-gray-200 overflow-hidden">
                        <Image
                          src={file.path}
                          alt={file.name}
                          width={48}
                          height={48}
                          className="object-cover"
                          unoptimized={true}
                          onError={(e) => {
                            e.target.style.display = 'none';
                            if (e.target.parentNode) {
                              e.target.parentNode.innerHTML = `<div class="w-12 h-12 flex items-center justify-center text-blue-500 border border-gray-200 rounded"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg></div>`;
                            }
                          }}
                        />
                      </div>
                    ) : (
                      <div className="w-12 h-12 flex items-center justify-center text-blue-500 border border-gray-200 rounded">
                        <FiFile size={24} />
                      </div>
                    )}
                  </div>
                  
                  <div 
                    className="flex-1 min-w-0 cursor-pointer" 
                    onClick={() => handleFilePreview(file)}
                  >
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {file.name}
                    </p>
                    <div className="flex items-center space-x-2">
                      <p className="text-xs text-gray-500">
                        {formatSize(file.size)} • 上传于 {formatDate(file.modifiedAt || file.createdAt)}
                      </p>
                      {/* === 修复：使用更简单直接的状态显示逻辑，参考files页面 === */}
                      <span 
                        className={`text-xs px-2 py-0.5 rounded-full cursor-pointer transition-colors ${
                          (() => {
                            const filePath = currentPath ? `${currentPath}/${file.name}` : file.name;
                            // === 修复：优先从syncStatuses获取状态，降级处理 ===
                            let currentStatus = '未同步'; // 默认状态
                            
                            // === 调试日志：记录状态获取过程 ===
                            console.log(`[状态显示] 文件: ${file.name}`, {
                              filePath,
                              portalName,
                              deviceId,
                              syncStatuses: Object.keys(syncStatuses),
                              fileStatus: file.status
                            });
                            
                            if (portalName && deviceId) {
                              // 如果有完整信息，使用完整路径
                              const fullLogicalPath = constructFullLogicalPath(filePath);
                              currentStatus = syncStatuses[fullLogicalPath] || '未同步';
                              console.log(`[状态显示] 使用完整路径: ${fullLogicalPath} -> ${currentStatus}`);
                            } else {
                              // 降级方案：直接从syncStatuses中查找可能的匹配
                              const possibleKeys = Object.keys(syncStatuses).filter(key => 
                                key.includes(file.name) || key.endsWith(`/${file.name}`)
                              );
                              console.log(`[状态显示] 降级方案，找到可能的键:`, possibleKeys);
                              
                              if (possibleKeys.length > 0) {
                                currentStatus = syncStatuses[possibleKeys[0]];
                                console.log(`[状态显示] 使用降级键: ${possibleKeys[0]} -> ${currentStatus}`);
                              }
                              // 还可以尝试使用file.status作为备用
                              if (!currentStatus || currentStatus === '未同步') {
                                if (file.status === 'synced' || file.status === 'uploaded') {
                                  currentStatus = '已同步';
                                  console.log(`[状态显示] 使用file.status备用: ${file.status} -> ${currentStatus}`);
                                } else if (file.status === 'pending') {
                                  currentStatus = '未同步';
                                  console.log(`[状态显示] 使用file.status备用: ${file.status} -> ${currentStatus}`);
                                } else if (file.status === 'uploading' || file.status === 'syncing') {
                                  currentStatus = '同步中';
                                  console.log(`[状态显示] 使用file.status备用: ${file.status} -> ${currentStatus}`);
                                } else if (file.status === 'failed') {
                                  currentStatus = '同步失败';
                                  console.log(`[状态显示] 使用file.status备用: ${file.status} -> ${currentStatus}`);
                                }
                              }
                            }
                            
                            console.log(`[状态显示] 最终状态: ${file.name} -> ${currentStatus}`);
                            
                            // === 修复：返回状态文本，不是样式 ===
                            return currentStatus;
                          })()}
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          
                          const filePath = currentPath ? `${currentPath}/${file.name}` : file.name;
                          
                          // === 修复：使用更智能的状态获取和切换逻辑 ===
                          let currentStatus = '未同步';
                          let statusKey = filePath; // 默认使用文件路径作为键
                          
                          if (portalName && deviceId) {
                            // 如果有完整信息，使用完整路径
                            statusKey = constructFullLogicalPath(filePath);
                            currentStatus = syncStatuses[statusKey] || '未同步';
                          } else {
                            // 降级方案：查找现有状态
                            const possibleKeys = Object.keys(syncStatuses).filter(key => 
                              key.includes(file.name) || key.endsWith(`/${file.name}`)
                            );
                            if (possibleKeys.length > 0) {
                              statusKey = possibleKeys[0];
                              currentStatus = syncStatuses[statusKey];
                            }
                          }
                          
                          // 切换状态：未同步 ⟷ 已同步
                          const newStatus = currentStatus === '已同步' ? '未同步' : '已同步';
                          
                          console.log(`手动切换文件状态: ${statusKey} ${currentStatus} -> ${newStatus}`);
                          
                          // 立即更新本地状态
                          setSyncStatuses(prevStatuses => ({
                            ...prevStatuses,
                            [statusKey]: newStatus
                          }));
                          
                          // 通过WebSocket或API更新后端状态
                          if (newStatus === '已同步') {
                            markFileAsSynced(filePath);
                          } else {
                            updateFileStatusToUnsynced(filePath);
                          }
                          
                          showToast(`文件 ${file.name} 状态已更新为: ${newStatus}`, 
                                   newStatus === '已同步' ? 'success' : 'info');
                        }}
                        title="点击切换同步状态"
                      >
                        {(() => {
                          const filePath = currentPath ? `${currentPath}/${file.name}` : file.name;
                          // === 修复：使用相同的降级状态获取逻辑 ===
                          let currentStatus = '未同步'; // 默认状态
                          
                          // === 调试日志：记录状态获取过程 ===
                          console.log(`[状态显示] 文件: ${file.name}`, {
                            filePath,
                            portalName,
                            deviceId,
                            syncStatuses: Object.keys(syncStatuses),
                            fileStatus: file.status
                          });
                          
                          if (portalName && deviceId) {
                            // 如果有完整信息，使用完整路径
                            const fullLogicalPath = constructFullLogicalPath(filePath);
                            currentStatus = syncStatuses[fullLogicalPath] || '未同步';
                            console.log(`[状态显示] 使用完整路径: ${fullLogicalPath} -> ${currentStatus}`);
                          } else {
                            // 降级方案：直接从syncStatuses中查找可能的匹配
                            const possibleKeys = Object.keys(syncStatuses).filter(key => 
                              key.includes(file.name) || key.endsWith(`/${file.name}`)
                            );
                            console.log(`[状态显示] 降级方案，找到可能的键:`, possibleKeys);
                            
                            if (possibleKeys.length > 0) {
                              currentStatus = syncStatuses[possibleKeys[0]];
                              console.log(`[状态显示] 使用降级键: ${possibleKeys[0]} -> ${currentStatus}`);
                            }
                            // 还可以尝试使用file.status作为备用
                            if (!currentStatus || currentStatus === '未同步') {
                              if (file.status === 'synced' || file.status === 'uploaded') {
                                currentStatus = '已同步';
                                console.log(`[状态显示] 使用file.status备用: ${file.status} -> ${currentStatus}`);
                              } else if (file.status === 'pending') {
                                currentStatus = '未同步';
                                console.log(`[状态显示] 使用file.status备用: ${file.status} -> ${currentStatus}`);
                              } else if (file.status === 'uploading' || file.status === 'syncing') {
                                currentStatus = '同步中';
                                console.log(`[状态显示] 使用file.status备用: ${file.status} -> ${currentStatus}`);
                              } else if (file.status === 'failed') {
                                currentStatus = '同步失败';
                                console.log(`[状态显示] 使用file.status备用: ${file.status} -> ${currentStatus}`);
                              }
                            }
                          }
                          
                          console.log(`[状态显示] 最终状态: ${file.name} -> ${currentStatus}`);
                          
                          // === 修复：返回状态文本，不是样式 ===
                          return currentStatus;
                        })()}
                      </span>
                    </div>
                  </div>
                  
                  {/* 文件操作按钮 */}
                  <div className="ml-2 sm:ml-4 flex space-x-1">
                    <button
                      onClick={() => handleFilePreview(file)}
                      className="p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 rounded-full"
                      title="预览文件"
                    >
                      <FiImage size={20} />
                    </button>
                    <a
                      href={file.path}
                      download={file.name}
                      className="p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 rounded-full"
                      title="下载文件"
                    >
                      <FiDownload size={20} />
                    </a>
                    <button
                      onClick={() => confirmDelete(file, false)}
                      className="p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-full"
                      title="删除文件"
                      disabled={deletingFile}
                    >
                      <FiTrash2 size={20} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      
      {/* 删除确认对话框 */}
      {showDeleteModal && fileToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="text-center">
              <FiAlertTriangle className="mx-auto h-12 w-12 text-red-500" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                确认删除{fileToDelete.isFolder ? '文件夹' : '文件'}？
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                您确定要删除{fileToDelete.isFolder ? '文件夹' : '文件'}"{fileToDelete.name}"
                {fileToDelete.isFolder ? `及其中的所有内容` : ''}吗？此操作无法撤销。
                {fileToDelete.isFolder && (
                  <span className="block mt-1 text-xs text-red-500">
                    注意：文件夹中包含 {fileToDelete.fileCount} 个文件，总大小 {formatSize(fileToDelete.size)}
                  </span>
                )}
              </p>
            </div>
            
            <div className="mt-6 flex justify-center space-x-3">
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setFileToDelete(null);
                }}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
                disabled={deletingFile}
              >
                取消
              </button>
              <button
                onClick={() => fileToDelete.isFolder ? handleDeleteFolder(fileToDelete) : handleDeleteFile(fileToDelete)}
                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center"
                disabled={deletingFile}
              >
                {deletingFile ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    删除中...
                  </>
                ) : (
                  <>删除</>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* 页脚 */}
      <div className="mt-6 text-center text-xs text-gray-500">
        File Cloud Transfer System • 安全便捷的文件传输服务
      </div>
    </div>
  )
} 