const { createServer } = require('http');
const { createServer: createHttpsServer } = require('https');
const { parse } = require('url');
const next = require('next');
const WebSocket = require('ws');
const fs = require('fs');
const fsPromises = require('fs/promises');
const path = require('path');

const dev = process.env.NODE_ENV !== 'production';
const hostname = process.env.HOSTNAME || '0.0.0.0'; // 监听所有接口，支持外部访问
const port = process.env.PORT || 3000; // Use environment variable or default
const httpsPort = process.env.HTTPS_PORT || 6655; // HTTPS端口，与开发端口保持一致
const app = next({ dev, hostname: 'localhost', port }); // Next.js内部仍使用localhost
const handle = app.getRequestHandler();

// SSL证书路径
const sslKeyPath = path.join(process.cwd(), 'ssl', 'server.key');
const sslCertPath = path.join(process.cwd(), 'ssl', 'server.crt');

// 检查SSL证书是否存在
const hasSSLCerts = fs.existsSync(sslKeyPath) && fs.existsSync(sslCertPath);

// --- File System Constants (adapted from API route) ---
const PUBLIC_UPLOADS_DIR = path.join(process.cwd(), 'public/uploads');
const PORTAL_UPLOADS_DIR = path.join(process.cwd(), 'uploads', 'portals');
const PORTALS_DATA_DIR = path.join(process.cwd(), 'data', 'upload-portals');
const PORTALS_FILE = path.join(PORTALS_DATA_DIR, 'portals.json');

// --- Helper Functions (adapted from API route) ---
const ensureDirectoryExists = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

const getPortals = () => {
  try {
    ensureDirectoryExists(PORTALS_DATA_DIR);
    if (!fs.existsSync(PORTALS_FILE)) return [];
    const data = fs.readFileSync(PORTALS_FILE, 'utf8');
    return JSON.parse(data || '[]'); // Handle empty file
  } catch (err) {
    console.error('Error reading portals file:', err);
    return [];
  }
};

const parseAdminPath = (reqPath) => {
  const normalizedReqPath = (reqPath || '/').replace(/^\/+/, '');
  const pathSegments = normalizedReqPath.split('/').filter(Boolean);
  const portals = getPortals();

  if (pathSegments.length === 0) {
    return { type: 'root', logicalPath: '/', physicalPath: '/', portals }; // Added physicalPath for root
  }

  const firstSegment = pathSegments[0];
  const potentialPortal = portals.find(p => p.name === firstSegment);

  if (potentialPortal) {
    const portalId = potentialPortal.id;
    const portalName = potentialPortal.name;
    const logicalPath = pathSegments.join('/').replace(/\\/g, '/');

    if (pathSegments.length === 1) {
      const physicalPath = path.join(PORTAL_UPLOADS_DIR, portalId);
      return { type: 'portal_root', portalId, portalName, physicalPath, logicalPath };
    } else {
      const deviceId = pathSegments[1];
      const relativePathInsideDevice = pathSegments.slice(2).join('/');
      const physicalPath = path.join(PORTAL_UPLOADS_DIR, portalId, deviceId, relativePathInsideDevice);
      const relativePath = path.join(deviceId, relativePathInsideDevice).replace(/\\/g, '/');
      return {
        type: 'portal_content',
        portalId,
        portalName,
        deviceId,
        physicalPath,
        logicalPath,
        relativePath
      };
    }
  } else {
    const physicalPath = path.join(PUBLIC_UPLOADS_DIR, normalizedReqPath);
    const logicalPath = normalizedReqPath;
    return { type: 'public', physicalPath, logicalPath };
  }
};

// --- Recursive File Tree Function ---
async function getAdminFileTreeRecursive(logicalPathReq) {
  const pathContext = parseAdminPath(logicalPathReq);
  const tree = { files: [], folders: [] };
  let currentPhysicalPath = '';
  let isRoot = false;

  console.log(`[getTree] Requested Logical Path: ${logicalPathReq}, Context:`, pathContext);

  try {
    if (pathContext.type === 'root') {
      isRoot = true;
      // 1. Handle Portals (as top-level folders)
      const portals = pathContext.portals || [];
      const portalNames = new Set();
      for (const portal of portals) {
        portalNames.add(portal.name);
        const portalFolderNode = {
          name: portal.name,
          filename: portal.name,
          isFolder: true,
          isPortal: true,
          path: portal.name, // Logical path
          createdAt: portal.createdAt,
          modifiedAt: portal.updatedAt,
          size: portal.stats?.totalSize || 0,
          fileCount: portal.stats?.uploads || 0,
          children: await getAdminFileTreeRecursive(portal.name) // Recursively get content
        };
        tree.folders.push(portalFolderNode);
      }

      // 2. Handle Public Root
      ensureDirectoryExists(PUBLIC_UPLOADS_DIR);
      const publicItems = await fsPromises.readdir(PUBLIC_UPLOADS_DIR, { withFileTypes: true });
      for (const item of publicItems) {
        const itemLogicalPath = item.name; // At root, logical == physical name
        // Skip items that have the same name as a portal
        if (portalNames.has(item.name)) {
            console.log(`[getTree] Skipping public item '${item.name}' as it conflicts with a portal name.`);
            continue;
        }
        
        const itemPhysicalPath = path.join(PUBLIC_UPLOADS_DIR, item.name);
        try {
          const stats = await fsPromises.stat(itemPhysicalPath);
          if (item.isDirectory()) {
            tree.folders.push({
              name: item.name,
              filename: item.name,
              isFolder: true,
              isPortal: false,
              path: itemLogicalPath,
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime,
              size: 0,
              children: await getAdminFileTreeRecursive(itemLogicalPath)
            });
          } else if (item.isFile()) {
            tree.files.push({
              name: item.name,
              filename: item.name,
              isFolder: false,
              isPortalFile: false,
              path: itemLogicalPath,
              size: stats.size,
              type: path.extname(item.name).slice(1).toLowerCase() || 'unknown',
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime
            });
          }
        } catch (statErr) {
          console.error(`[getTree] Error stating public item ${itemPhysicalPath}:`, statErr);
        }
      }
    } else {
      // Handle non-root paths (public subdir, portal root, portal content)
      currentPhysicalPath = pathContext.physicalPath;
      ensureDirectoryExists(currentPhysicalPath);
      const items = await fsPromises.readdir(currentPhysicalPath, { withFileTypes: true });

      for (const item of items) {
        const itemLogicalPath = path.join(pathContext.logicalPath, item.name).replace(/\\/g, '/');
        const itemPhysicalPath = path.join(currentPhysicalPath, item.name);
        const isDeviceFolder = pathContext.type === 'portal_root'; // Devices are direct children of portal_root
        const isPortalFile = pathContext.type === 'portal_content';

        try {
          const stats = await fsPromises.stat(itemPhysicalPath);
          if (item.isDirectory()) {
            tree.folders.push({
              name: item.name,
              filename: item.name,
              isFolder: true,
              isPortal: false,
              isDeviceFolder: isDeviceFolder,
              portalId: pathContext.portalId || null,
              deviceId: isDeviceFolder ? item.name : (pathContext.deviceId || null),
              path: itemLogicalPath,
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime,
              size: 0,
              children: await getAdminFileTreeRecursive(itemLogicalPath)
            });
          } else if (item.isFile()) {
            tree.files.push({
              name: item.name,
              filename: item.name,
              isFolder: false,
              isPortalFile: isPortalFile,
              portalId: pathContext.portalId || null,
              deviceId: pathContext.deviceId || null,
              path: itemLogicalPath,
              size: stats.size,
              type: path.extname(item.name).slice(1).toLowerCase() || 'unknown',
              createdAt: stats.birthtime,
              modifiedAt: stats.mtime
            });
          }
        } catch (statErr) {
          console.error(`[getTree] Error stating item ${itemPhysicalPath}:`, statErr);
        }
      }
    }
  } catch (err) {
    // Log error but return empty tree for this level if directory doesn't exist or is inaccessible
    if (err.code === 'ENOENT') {
      console.warn(`[getTree] Directory not found: ${currentPhysicalPath} (Logical: ${logicalPathReq})`);
    } else {
      console.error(`[getTree] Error reading directory ${currentPhysicalPath} (Logical: ${logicalPathReq}):`, err);
    }
  }
  return tree;
}

// --- 文件查找辅助函数 ---
async function findFileByName(fileName) {
  try {
    console.log(`[findFileByName] 搜索文件: ${fileName}`);
    
    // 递归搜索所有文件
    const searchInTree = async (tree, currentPath = '') => {
      // 搜索当前级别的文件
      for (const file of tree.files || []) {
        if (file.name === fileName || file.filename === fileName) {
          console.log(`[findFileByName] 找到文件: ${file.path}`);
          return {
            ...file,
            path: file.path || file.originalPath
          };
        }
      }
      
      // 递归搜索子文件夹
      for (const folder of tree.folders || []) {
        if (folder.children) {
          const result = await searchInTree(folder.children, folder.path);
          if (result) return result;
        }
      }
      
      return null;
    };
    
    // 从根目录开始搜索
    const rootTree = await getAdminFileTreeRecursive('/');
    const result = await searchInTree(rootTree);
    
    if (result) {
      console.log(`[findFileByName] 文件搜索成功: ${result.path}`);
    } else {
      console.log(`[findFileByName] 未找到文件: ${fileName}`);
    }
    
    return result;
  } catch (error) {
    console.error(`[findFileByName] 搜索文件时出错:`, error);
    return null;
  }
}

// --- Next.js and HTTP Server Setup ---
app.prepare().then(() => {
  const httpServer = createServer((req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      const { pathname } = parsedUrl;
      
      // === 自定义路由处理 ===

      // 处理CORS预检请求
      if (req.method === 'OPTIONS') {
        res.writeHead(200, {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Max-Age': '86400'
        });
        res.end();
        return;
      }

      // 1. API路由：文件信息查询
      if (pathname === '/api/file-info' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', async () => {
          try {
            const { fileName } = JSON.parse(body);
            const fileInfo = await findFileByName(fileName);

            if (fileInfo) {
              res.writeHead(200, {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type'
              });
              res.end(JSON.stringify({
                success: true,
                results: [{
                  ...fileInfo,
                  accessUrl: `/api/file/${encodeURIComponent(fileInfo.path)}`
                }]
              }));
            } else {
              res.writeHead(404, {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              });
              res.end(JSON.stringify({
                success: false,
                error: 'File not found'
              }));
            }
          } catch (error) {
            res.writeHead(500, {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            });
            res.end(JSON.stringify({
              success: false,
              error: error.message
            }));
          }
        });
        return;
      }
      
      // 2. API路由：文件访问
      if (pathname.startsWith('/api/file/')) {
        const encodedPath = pathname.replace('/api/file/', '');
        const filePath = decodeURIComponent(encodedPath);
        
        try {
          const pathContext = parseAdminPath(filePath);
          let physicalFilePath;
          
          if (pathContext.type === 'portal_content') {
            // Portal文件
            physicalFilePath = pathContext.physicalPath;
          } else if (pathContext.type === 'public') {
            // 公共文件
            physicalFilePath = pathContext.physicalPath;
          } else {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
          }
          
          // 检查文件是否存在
          if (!fs.existsSync(physicalFilePath)) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
          }
          
          // 获取文件信息
          const stats = fs.statSync(physicalFilePath);
          const ext = path.extname(physicalFilePath).toLowerCase();
          
          // 设置MIME类型
          const mimeTypes = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.svg': 'image/svg+xml',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain'
          };
          const contentType = mimeTypes[ext] || 'application/octet-stream';
          
          // 设置响应头
          res.writeHead(200, {
            'Content-Type': contentType,
            'Content-Length': stats.size,
            'Cache-Control': 'public, max-age=3600',
            'Access-Control-Allow-Origin': '*'
          });
          
          // 流式传输文件
          const fileStream = fs.createReadStream(physicalFilePath);
          fileStream.pipe(res);
          
          fileStream.on('error', (error) => {
            console.error('文件流错误:', error);
            if (!res.headersSent) {
              res.writeHead(500, { 'Content-Type': 'text/plain' });
              res.end('Internal Server Error');
            }
          });
          
          return;
        } catch (error) {
          console.error('文件访问错误:', error);
          res.writeHead(500, { 'Content-Type': 'text/plain' });
          res.end('Internal Server Error');
          return;
        }
      }
      
      // 3. 静态文件路由：兼容旧的uploads路径
      if (pathname.startsWith('/uploads/')) {
        const relativePath = pathname.replace('/uploads/', '');
        
        // 首先尝试公共uploads目录
        let filePath = path.join(PUBLIC_UPLOADS_DIR, relativePath);
        
        if (fs.existsSync(filePath)) {
          const stats = fs.statSync(filePath);
          if (stats.isFile()) {
            const ext = path.extname(filePath).toLowerCase();
            const mimeTypes = {
              '.jpg': 'image/jpeg',
              '.jpeg': 'image/jpeg',
              '.png': 'image/png',
              '.gif': 'image/gif',
              '.webp': 'image/webp'
            };
            const contentType = mimeTypes[ext] || 'application/octet-stream';
            
            res.writeHead(200, {
              'Content-Type': contentType,
              'Content-Length': stats.size,
              'Cache-Control': 'public, max-age=3600'
            });
            
            const fileStream = fs.createReadStream(filePath);
            fileStream.pipe(res);
            return;
          }
        }
        
        // 如果公共目录没有找到，返回404
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('File not found');
        return;
      }
      
      // 默认处理：交给Next.js
      handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // --- HTTPS Server Setup (if certificates exist) ---
  let httpsServer = null;
  if (hasSSLCerts) {
    try {
      const httpsOptions = {
        key: fs.readFileSync(sslKeyPath),
        cert: fs.readFileSync(sslCertPath)
      };
      
      httpsServer = createHttpsServer(httpsOptions, (req, res) => {
        try {
          const parsedUrl = parse(req.url, true);
          const { pathname } = parsedUrl;
          
          // === HTTPS的自定义路由处理 (与HTTP相同) ===

          // 处理CORS预检请求
          if (req.method === 'OPTIONS') {
            res.writeHead(200, {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'Access-Control-Max-Age': '86400'
            });
            res.end();
            return;
          }

          // 1. API路由：文件信息查询
          if (pathname === '/api/file-info' && req.method === 'POST') {
            let body = '';
            req.on('data', chunk => body += chunk);
            req.on('end', async () => {
              try {
                const { fileName } = JSON.parse(body);
                const fileInfo = await findFileByName(fileName);
                
                if (fileInfo) {
                  res.writeHead(200, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type'
                  });
                  res.end(JSON.stringify({
                    success: true,
                    results: [{
                      ...fileInfo,
                      accessUrl: `/api/file/${encodeURIComponent(fileInfo.path)}`
                    }]
                  }));
                } else {
                  res.writeHead(404, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                  });
                  res.end(JSON.stringify({
                    success: false,
                    error: 'File not found'
                  }));
                }
              } catch (error) {
                res.writeHead(500, {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                });
                res.end(JSON.stringify({
                  success: false,
                  error: error.message
                }));
              }
            });
            return;
          }
          
          // 2. API路由：文件访问
          if (pathname.startsWith('/api/file/')) {
            const encodedPath = pathname.replace('/api/file/', '');
            const filePath = decodeURIComponent(encodedPath);
            
            try {
              const pathContext = parseAdminPath(filePath);
              let physicalFilePath;
              
              if (pathContext.type === 'portal_content') {
                // Portal文件
                physicalFilePath = pathContext.physicalPath;
              } else if (pathContext.type === 'public') {
                // 公共文件
                physicalFilePath = pathContext.physicalPath;
              } else {
                res.writeHead(404, { 'Content-Type': 'text/plain' });
                res.end('File not found');
                return;
              }
              
              // 检查文件是否存在
              if (!fs.existsSync(physicalFilePath)) {
                res.writeHead(404, { 'Content-Type': 'text/plain' });
                res.end('File not found');
                return;
              }
              
              // 获取文件信息
              const stats = fs.statSync(physicalFilePath);
              const ext = path.extname(physicalFilePath).toLowerCase();
              
              // 设置MIME类型
              const mimeTypes = {
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.png': 'image/png',
                '.gif': 'image/gif',
                '.webp': 'image/webp',
                '.svg': 'image/svg+xml',
                '.pdf': 'application/pdf',
                '.txt': 'text/plain'
              };
              const contentType = mimeTypes[ext] || 'application/octet-stream';
              
              // 设置响应头
              res.writeHead(200, {
                'Content-Type': contentType,
                'Content-Length': stats.size,
                'Cache-Control': 'public, max-age=3600',
                'Access-Control-Allow-Origin': '*'
              });
              
              // 流式传输文件
              const fileStream = fs.createReadStream(physicalFilePath);
              fileStream.pipe(res);
              
              fileStream.on('error', (error) => {
                console.error('HTTPS文件流错误:', error);
                if (!res.headersSent) {
                  res.writeHead(500, { 'Content-Type': 'text/plain' });
                  res.end('Internal Server Error');
                }
              });
              
              return;
            } catch (error) {
              console.error('HTTPS文件访问错误:', error);
              res.writeHead(500, { 'Content-Type': 'text/plain' });
              res.end('Internal Server Error');
              return;
            }
          }
          
          // 3. 静态文件路由：兼容旧的uploads路径
          if (pathname.startsWith('/uploads/')) {
            const relativePath = pathname.replace('/uploads/', '');
            
            // 首先尝试公共uploads目录
            let filePath = path.join(PUBLIC_UPLOADS_DIR, relativePath);
            
            if (fs.existsSync(filePath)) {
              const stats = fs.statSync(filePath);
              if (stats.isFile()) {
                const ext = path.extname(filePath).toLowerCase();
                const mimeTypes = {
                  '.jpg': 'image/jpeg',
                  '.jpeg': 'image/jpeg',
                  '.png': 'image/png',
                  '.gif': 'image/gif',
                  '.webp': 'image/webp'
                };
                const contentType = mimeTypes[ext] || 'application/octet-stream';
                
                res.writeHead(200, {
                  'Content-Type': contentType,
                  'Content-Length': stats.size,
                  'Cache-Control': 'public, max-age=3600'
                });
                
                const fileStream = fs.createReadStream(filePath);
                fileStream.pipe(res);
                return;
              }
            }
            
            // 如果公共目录没有找到，返回404
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
          }
          
          // 默认处理：交给Next.js
          handle(req, res, parsedUrl);
        } catch (err) {
          console.error('Error occurred handling HTTPS', req.url, err);
          res.statusCode = 500;
          res.end('internal server error');
        }
      });
      
      console.log('✅ SSL certificates found, HTTPS server will be enabled');
    } catch (sslError) {
      console.error('❌ Error loading SSL certificates:', sslError.message);
      console.log('🔧 HTTPS server will not be started');
    }
  } else {
    console.log('📝 SSL certificates not found at:');
    console.log(`   Key: ${sslKeyPath}`);
    console.log(`   Cert: ${sslCertPath}`);
    console.log('💡 To enable HTTPS, run: npm run generate-ssl');
  }

  // --- WebSocket Server Setup ---
  const wss = new WebSocket.Server({ server: httpServer });

  wss.on('connection', (ws) => {
    console.log('[WS] Client Connected');

    ws.on('message', async (messageBuffer) => {
      const messageString = messageBuffer.toString();
      console.log('[WS] Received message:', messageString);
      let request;
      try {
        request = JSON.parse(messageString);
      } catch (e) {
        console.error('[WS] Invalid JSON received:', e);
        ws.send(JSON.stringify({ type: 'error', message: 'Invalid JSON format' }));
        return;
      }

      // --- Message Handling Logic ---
      if (request.action === 'get_full_admin_file_tree') {
        console.log('[WS] Processing get_full_admin_file_tree request...');
        try {
          const adminTree = await getAdminFileTreeRecursive('/'); // Start recursion from root
          console.log('[WS] Sending file tree to client.');
          ws.send(JSON.stringify({ type: 'admin_file_tree', payload: adminTree }));
        } catch (error) {
          console.error('[WS] Error generating file tree:', error);
          ws.send(JSON.stringify({ type: 'error', message: 'Failed to generate file tree' }));
        }
      } else {
        console.log('[WS] Unknown action:', request.action);
        // Optionally send an error for unknown actions
        // ws.send(JSON.stringify({ type: 'error', message: `Unknown action: ${request.action}` }));
      }
    });

    ws.on('close', () => {
      console.log('[WS] Client Disconnected');
    });

    ws.on('error', (error) => {
      console.error('[WS] Error:', error);
    });
  });

  // --- Start HTTP Server ---
  httpServer
    .listen(port, () => {
      console.log(`🚀 HTTP Server ready on http://${hostname}:${port}`);
      console.log(`🔌 WebSocket Server listening on ws://${hostname}:${port}`);
    })
    .on('error', (err) => {
      console.error('❌ HTTP Server Error:', err);
      process.exit(1);
    });

  // --- Start HTTPS Server (if available) ---
  if (httpsServer) {
    httpsServer
      .listen(httpsPort, () => {
        console.log(`🔒 HTTPS Server ready on https://${hostname}:${httpsPort}`);
        console.log(`📱 Camera features available on HTTPS endpoint`);
      })
      .on('error', (err) => {
        console.error('❌ HTTPS Server Error:', err);
        console.log('📝 HTTPS server failed to start, continuing with HTTP only');
      });
  }

}).catch((ex) => {
  console.error('❌ Next.js App Preparation Error:', ex.stack);
  process.exit(1);
}); 