# 文件云流转助手 - 安装指南

## 📋 系统要求

- **浏览器**: Google Chrome 88+ 或基于Chromium的浏览器
- **操作系统**: Windows 10/11, macOS 10.14+, Linux
- **网络**: 能够访问内网服务器 (***************)
- **权限**: 管理员权限（用于安装扩展）

## 🚀 安装步骤

### 方法一：开发者模式安装（推荐）

1. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开关启用开发者模式

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择 `chrome-extension` 文件夹
   - 点击"选择文件夹"

4. **验证安装**
   - 扩展应该出现在扩展列表中
   - 浏览器工具栏应该显示插件图标
   - 状态应该显示为"已启用"

### 方法二：打包安装

1. **打包扩展**
   - 在扩展管理页面点击"打包扩展程序"
   - 选择 `chrome-extension` 文件夹作为扩展根目录
   - 点击"打包扩展程序"生成 `.crx` 文件

2. **安装打包文件**
   - 将生成的 `.crx` 文件拖拽到扩展管理页面
   - 点击"添加扩展程序"确认安装

## ⚙️ 配置设置

### 服务器连接配置

安装完成后需要确保以下配置正确：

1. **后端服务器地址**: `***************`
2. **HTTP服务端口**: `3000`
3. **WebSocket端口**: `6656`

### 权限确认

扩展需要以下权限：
- ✅ **存储权限** (`storage`) - 保存用户设置和缓存
- ✅ **活动标签页** (`activeTab`) - 与当前页面交互
- ✅ **脚本注入** (`scripting`) - 在页面中执行自动化脚本
- ✅ **标签页管理** (`tabs`) - 管理浏览器标签页
- ✅ **所有网站访问** (`<all_urls>`) - 访问云盘网站

## 🔧 首次使用

### 1. 检查连接状态
- 点击浏览器工具栏中的插件图标
- 查看连接状态指示器
- 确保显示"已连接"状态

### 2. 访问目标网站
- 打开政府云盘网站：`https://yunpan.gdcourts.gov.cn`
- 登录您的账户
- 插件应该自动检测到页面并开始工作

### 3. 测试功能
- 尝试上传一个测试文件
- 检查插件界面中的文件状态
- 验证自动化流程是否正常工作

## 🛠️ 故障排除

### 常见问题

#### 问题1：扩展无法加载
**症状**: 加载扩展时出现错误
**解决方案**:
- 确保选择了正确的文件夹（包含 `manifest.json`）
- 检查文件权限，确保Chrome可以读取文件
- 尝试重新启动Chrome浏览器

#### 问题2：无法连接服务器
**症状**: 插件显示"连接失败"
**解决方案**:
- 检查网络连接
- 确认服务器地址 `***************` 可访问
- 检查防火墙设置
- 联系系统管理员确认服务器状态

#### 问题3：自动化功能不工作
**症状**: 文件上传不自动执行
**解决方案**:
- 刷新云盘页面
- 检查页面是否完全加载
- 查看浏览器控制台是否有错误信息
- 尝试手动点击插件图标重新激活

#### 问题4：文件状态不更新
**症状**: 上传的文件状态显示不正确
**解决方案**:
- 点击插件界面中的刷新按钮
- 检查WebSocket连接状态
- 清除浏览器缓存和插件数据

### 调试工具

插件提供了多个调试工具，位于 `tests-and-debug/` 文件夹：

1. **WebSocket连接测试**: `extension_websocket_test.html`
2. **连接诊断工具**: `websocket_diagnostic.html`
3. **连接监控工具**: `websocket_monitor.html`
4. **上传调试工具**: `upload_debug_tool.html`

使用方法：
- 在Chrome中打开对应的HTML文件
- 按照页面提示进行测试
- 查看控制台输出获取详细信息

## 📞 技术支持

### 日志收集
如需技术支持，请提供以下信息：

1. **浏览器信息**:
   - Chrome版本号
   - 操作系统版本

2. **错误日志**:
   - 打开Chrome开发者工具 (F12)
   - 切换到"控制台"标签页
   - 复制所有错误信息

3. **网络状态**:
   - 确认是否能访问 `***************:6655`
   - 测试WebSocket连接 `ws://***************:6656`

### 联系方式
- 技术支持邮箱: [请联系系统管理员]
- 内部技术支持: [请联系开发团队]

## 🔄 更新说明

### 自动更新
- 开发者模式安装的扩展不会自动更新
- 需要手动重新加载扩展来获取更新

### 手动更新步骤
1. 下载最新版本的插件文件
2. 在扩展管理页面点击"重新加载"按钮
3. 或者删除旧版本后重新安装

### 数据备份
更新前建议备份：
- 浏览器书签和设置
- 插件的配置数据（如有自定义设置）

---

**安装完成后，您就可以开始使用文件云流转助手来自动化您的文件管理工作了！**

如有任何问题，请参考故障排除部分或联系技术支持。
